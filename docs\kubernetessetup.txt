# ProposalPilot Kubernetes Setup Guide

## Prerequisites
- Docker Desktop with Kubernetes enabled
- kubectl installed
- He<PERSON> installed

## 1. Build Docker Image
```bash
docker build -t proposalpilot .
```

## 2. Deploy Application with Helm (NEW)
```bash
# Create namespace and deploy
helm install proposalpilot ./proposalpilot-chart -n proposalpilot --create-namespace
** OR USE IT with the secrets-values.yaml file:**

helm install proposalpilot ./proposalpilot-chart -n proposalpilot --create-namespace -f proposalpilot-chart/secrets-values.yaml
# heml Update
helm upgrade --install proposalpilot ./proposalpilot-chart -n proposalpilot \
  --set secrets.geminiApiKey=$(echo -n "AIzaSyBWKWSbWhAe3GrSI0ko1MVOKvvRt70UosA" | base64) \
  --set secrets.braveApiKey=$(echo -n "BSAtPiv3uykgac7s8vuOMdHR3p1f4Un" | base64) \
  --set secrets.serperApiKey=$(echo -n "eb3de50122fcdd22972a065e4bab9f7b77959c90" | base64)
                                      OR
 #(This is done after the helm install proposalpilot ./proposalpilot-chart -n proposalpilot --create-namespace)
 helm upgrade --install proposalpilot ./proposalpilot-chart -n proposalpilot -f proposalpilot-chart/secrets-values.yaml

  OR (Currently implemented to UPGRADE)
  helm upgrade --install proposalpilot ./proposalpilot-chart -n proposalpilot -f secrets-values.yaml
  helm upgrade --install proposalpilot ./proposalpilot-chart -n proposalpilot -f secrets-values.yaml
  ## Run from project root
  helm upgrade --install proposalpilot ./proposalpilot-chart -n proposalpilot -f proposalpilot-chart/secrets-values.yaml

------------------------------------------------------------------------------------------------------------------------
# Verify POSTGRESS deployment
# Check if PostgreSQL pod is running
kubectl get pods -n proposalpilot -l app.kubernetes.io/name=postgresql

# Check the logs
kubectl logs -n proposalpilot -l app.kubernetes.io/name=postgresql

# Test the database connection
# Get a shell to the PostgreSQL pod
kubectl exec -n proposalpilot -it $(kubectl get pods -n proposalpilot -l app.kubernetes.io/name=postgresql -o name) -- bash

# Connect to PostgreSQL (port 5432)
PGPASSWORD='czrWNlWK9O' psql -U postgres -h localhost -p 5432

#postgress commands to test
-- List all databases
\l

-- List all users
\du

-- Connect to the proposalpilot database
\c proposalpilot

-- List tables
\dt
------------------------------------------------------------------------------------------------------------------------

## 3. Access the Application
### Option 1: Port Forwarding (Recommended for Development)
```bash
kubectl port-forward -n proposalpilot svc/proposalpilot 3000:3000
```
Access at: http://localhost:3000

### Option 2: Expose as NodePort
```bash
kubectl patch svc -n proposalpilot proposalpilot -p '{\"spec\":{\"type\":\"NodePort\"}}'
kubectl get svc -n proposalpilot
```

## 4. Verify Deployment
```bash
# Check all resources
kubectl get all -n proposalpilot

# Check pods
kubectl get pods -n proposalpilot

# Check services
kubectl get svc -n proposalpilot

# Check deployment status
kubectl get deployment -n proposalpilot
```

## 5. View Logs
```bash
kubectl logs -n proposalpilot -l app=proposalpilot
kubectl logs -n proposalpilot <pod-name> -f
```

## 6. Update Deployment
After making changes to your application:
```bash
# Rebuild the Docker image
docker build -t proposalpilot .

# Restart the deployment
kubectl rollout restart deployment/proposalpilot -n proposalpilot
```

## 7. Clean Up
```bash
# Uninstall Helm release
helm uninstall proposalpilot -n proposalpilot

# Delete namespace (removes all resources)
kubectl delete namespace proposalpilot
```

## Troubleshooting
- If port 3000 is in use, change the port in the port-forward command
- Check Docker Desktop's Kubernetes is running
- Ensure all pods are in 'Running' state with `kubectl get pods -n proposalpilot`
