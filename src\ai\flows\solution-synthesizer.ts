
'use server';

/**
 * @fileOverview Generates a 'Solution Overview' section with suggested OEM solutions based on the RFP requirements, augmented by web research.
 *
 * - generateSolutionOverview - A function that generates the solution overview.
 * - SolutionOverviewInput - The input type for the generateSolutionOverview function.
 * - SolutionOverviewOutput - The return type for the generateSolutionOverview function.
 */

import {ai} from '@/ai/genkit';
import {z} from 'genkit';
import {performWebResearch} from '@/ai/tools/web-researcher'; // Import the tool
import { UserPreferencesSchema } from '../schemas/user-preferences';
import { convertMermaidToSvgDataUri } from '@/lib/mermaid-server-utils';

/**
 * Cleans and escapes a JSON string to ensure it's valid JSON.
 * Handles common issues like unescaped newlines and special characters.
 */
function cleanJsonString(str: string): string {
  if (!str) return '';
  
  // First, try to parse and stringify to catch any JSON parsing issues
  try {
    // If it's already valid JSON, return it as is
    JSON.parse(str);
    return str;
  } catch (e) {
    // If not valid JSON, clean it up
    // Replace newlines with escaped newlines
    let cleaned = str
      .replace(/\\/g, '\\\\') // Escape backslashes first
      .replace(/\n/g, '\\n')     // Escape newlines
      .replace(/\r/g, '\\r')     // Escape carriage returns
      .replace(/\t/g, '\\t')     // Escape tabs
      .replace(/\f/g, '\\f')     // Escape form feeds
      .replace(/\b/g, '\\b')     // Escape backspace
      .replace(/\v/g, '\\v')     // Escape vertical tab
      .replace(/"/g, '\\"');     // Escape double quotes
      
    // Try to parse again to verify
    try {
      JSON.parse(`{"test":"${cleaned}"}`);
      return cleaned;
    } catch (e) {
      console.warn('Failed to clean JSON string, using fallback cleanup');
      // If still failing, use a more aggressive cleanup
      return cleaned
        .replace(/[\x00-\x1F\x7F-\x9F]/g, '') // Remove control characters
        .replace(/[^\x20-\x7E\n\r\t]/g, '');  // Remove non-printable characters
    }
  }
}

/**
 * Safely parses JSON with proper error handling and cleaning
 */
function safeJsonParse(jsonString: string): any {
  try {
    return JSON.parse(jsonString);
  } catch (e) {
    console.error('Failed to parse JSON, attempting cleanup', e);
    try {
      // Try to clean and parse again
      const cleaned = cleanJsonString(jsonString);
      return JSON.parse(cleaned);
    } catch (cleanError) {
      console.error('Failed to clean and parse JSON', cleanError);
      throw new Error('Failed to parse JSON response after cleanup');
    }
  }
}



const SolutionOverviewInputSchema = z.object({
  rfpRequirements: z
    .string()
    .describe('The requirements outlined in the RFP document.'),
  // Enhanced input with structured requirements from Requirements Analysis Agent
  functionalRequirements: z.array(z.string()).optional().describe('List of functional requirements from requirements analysis.'),
  nonFunctionalRequirements: z.array(z.string()).optional().describe('List of non-functional requirements from requirements analysis.'),
  technicalConstraints: z.array(z.string()).optional().describe('List of technical constraints from requirements analysis.'),
  complianceRequirements: z.array(z.string()).optional().describe('List of compliance requirements from requirements analysis.'),
  integrationRequirements: z.array(z.string()).optional().describe('List of integration requirements from requirements analysis.'),
  userPreferences: UserPreferencesSchema.optional().describe('User preferences and considerations to guide solution design'),
  // Internal flag for fallback mode
  _fallbackMode: z.boolean().optional().default(false).describe('Internal flag indicating fallback mode without web research'),
});
export type SolutionOverviewInput = z.infer<typeof SolutionOverviewInputSchema>;

const SolutionOverviewOutputSchema = z.object({
  solutionOverview: z.string().describe('A comprehensive solution overview in Markdown format, based on the RFP requirements, including suggested OEM solutions and relevant sub-sections, augmented by web research.'),
  suggestedOemSolutions: z.array(z.string()).describe('List of suggested OEM solutions based on the RFP requirements'),
  // Enhanced output with structured data for Technical Implementation Agent
  proposedArchitecture: z.string().describe('High-level architecture description for the proposed solution.'),
  technologyStack: z.array(z.string()).describe('List of recommended technologies, frameworks, and platforms.'),
  keyFeatures: z.array(z.string()).describe('List of key features and capabilities of the proposed solution.'),
  securityConsiderations: z.array(z.string()).describe('Security measures and considerations for the solution.'),
  scalabilityApproach: z.string().describe('Approach to handling scalability requirements.'),
  integrationStrategy: z.string().describe('Strategy for integrating with existing systems and third-party services.'),
});
export type SolutionOverviewOutput = z.infer<typeof SolutionOverviewOutputSchema>;


async function enhanceMarkdownWithMermaidImages(markdownContent: string): Promise<string> {
  if (!markdownContent) return '';
  const mermaidRegex = /```mermaid\n([\s\S]*?)\n```/g;
  let finalMarkdown = markdownContent;
  
  // Collect all promises for SVG conversion
  const processingPromises: Promise<{ originalBlock: string; imageTag: string; startIndex: number }>[] = [];
  let match;

  // First pass: find all matches and start conversion
  while ((match = mermaidRegex.exec(markdownContent)) !== null) {
    const originalBlock = match[0];
    const mermaidScript = match[1];
    const startIndex = match.index; // Store index for stable replacements later

    processingPromises.push(
      convertMermaidToSvgDataUri(mermaidScript)
        .then(svgDataUri => ({
          originalBlock,
          imageTag: `![Mermaid Diagram](${svgDataUri})`,
          startIndex
        }))
        .catch(error => {
          console.error(`Failed to convert Mermaid block to SVG: ${error}`);
          return {
            originalBlock,
            imageTag: `${originalBlock}\n\n<!-- Mermaid SVG conversion failed: ${error.message} -->`,
            startIndex
          };
        })
    );
  }

  const replacements = await Promise.all(processingPromises);

  // Sort replacements by startIndex in descending order to avoid index shifts during replacement
  replacements.sort((a, b) => b.startIndex - a.startIndex);

  // Second pass: apply replacements
  for (const rep of replacements) {
    finalMarkdown = finalMarkdown.substring(0, rep.startIndex) + 
                    rep.imageTag + 
                    finalMarkdown.substring(rep.startIndex + rep.originalBlock.length);
  }

  return finalMarkdown;
}



export async function generateSolutionOverview(input: SolutionOverviewInput): Promise<SolutionOverviewOutput> {
  try {
    return await solutionSynthesizerFlow(input);
  } catch (error) {
    console.error('Error in generateSolutionOverview:', error);
    
    // Handle network-related errors specifically
    if (error instanceof Error && (
      error.message.includes('fetch failed') ||
      error.message.includes('network') ||
      error.message.includes('ECONNREFUSED') ||
      error.message.includes('ENOTFOUND') ||
      error.message.includes('certificate')
    )) {
      console.warn('Network-related error detected, falling back to local processing without web research');
      // Create a modified input without web research
      const localInput = {
        ...input,
        // Add a flag to indicate we're in fallback mode
        _fallbackMode: true as const
      };
      return solutionSynthesizerFlow(localInput);
    }
    
    // Re-throw other errors
    throw error;
  }
}

const solutionSynthesizerPrompt = ai.definePrompt({
  name: 'solutionSynthesizerPrompt',
  input: {schema: SolutionOverviewInputSchema},
  output: {schema: SolutionOverviewOutputSchema},
  tools: [performWebResearch], // Make the tool available to the prompt
  prompt: `You are an expert solution architect specializing in crafting comprehensive solution overviews for software and solution-related RFPs.
Based on the RFP requirements and structured requirements analysis provided{{#unless _fallbackMode}}, and **leveraging comprehensive web research from multiple sources (Serper and Brave Search) for current trends, technologies, and best practices**{{/unless}}, you will generate a detailed 'Solution Overview' section.

**Input Data:**
RFP Requirements: {{{rfpRequirements}}}

{{#if functionalRequirements}}
Functional Requirements:
{{#each functionalRequirements}}
- {{this}}
{{/each}}
{{/if}}

{{#if nonFunctionalRequirements}}
Non-Functional Requirements:
{{#each nonFunctionalRequirements}}
- {{this}}
{{/each}}
{{/if}}

{{#if technicalConstraints}}
Technical Constraints:
{{#each technicalConstraints}}
- {{this}}
{{/each}}
{{/if}}

{{#if integrationRequirements}}
Integration Requirements:
{{#each integrationRequirements}}
- {{this}}
{{/each}}
{{/if}}

{{#if complianceRequirements}}
Compliance Requirements:
{{#each complianceRequirements}}
- {{this}}
{{/each}}
{{/if}}

{{#if userPreferences}}
**USER PREFERENCES & CONSIDERATIONS:**
{{#if userPreferences.preferredTechnologies}}
Preferred Technologies: {{#each userPreferences.preferredTechnologies}}{{this}}{{#unless @last}}, {{/unless}}{{/each}}
{{/if}}
{{#if userPreferences.technicalConstraints}}
Technical Constraints: {{#each userPreferences.technicalConstraints}}{{this}}{{#unless @last}}, {{/unless}}{{/each}}
{{/if}}
{{#if userPreferences.budgetConsiderations}}
Budget Considerations: {{userPreferences.budgetConsiderations}}
{{/if}}
{{#if userPreferences.timelineConstraints}}
Timeline Constraints: {{userPreferences.timelineConstraints}}
{{/if}}
{{#if userPreferences.complianceRequirements}}
Additional Compliance Requirements: {{#each userPreferences.complianceRequirements}}{{this}}{{#unless @last}}, {{/unless}}{{/each}}
{{/if}}
{{#if userPreferences.integrationRequirements}}
Additional Integration Requirements: {{#each userPreferences.integrationRequirements}}{{this}}{{#unless @last}}, {{/unless}}{{/each}}
{{/if}}
{{#if userPreferences.scalabilityRequirements}}
Scalability Requirements: {{userPreferences.scalabilityRequirements}}
{{/if}}
{{#if userPreferences.securityRequirements}}
Security Requirements: {{#each userPreferences.securityRequirements}}{{this}}{{#unless @last}}, {{/unless}}{{/each}}
{{/if}}
{{#if userPreferences.additionalConsiderations}}
Additional Considerations: {{userPreferences.additionalConsiderations}}
{{/if}}

**IMPORTANT:** Prioritize the user's preferred technologies and constraints when designing the solution. Ensure the proposed architecture and technology stack align with these preferences where possible.
{{/if}}

{{#unless _fallbackMode}}
**Instructions for using Web Research:**
- Analyze the requirements to identify key technical concepts, technologies, architectural patterns, or industry challenges where up-to-date information would enhance the solution.
- Use the 'performWebResearch' tool to gather real-time insights. Make multiple calls for different aspects if needed.
- Research current best practices for the specific technologies, compliance requirements, and architectural patterns mentioned.
- Integrate findings naturally to demonstrate current, well-researched understanding.
{{else}}
**Note: Web research is currently unavailable. Proceeding with general best practices and knowledge.**
{{/unless}}

**CRITICAL: You MUST respond with valid JSON only. No additional text before or after the JSON.**

**IMPORTANT OUTPUT FORMAT:**
You must return a JSON object with exactly this structure. ALL 8 FIELDS ARE REQUIRED:
{
  "solutionOverview": "Your comprehensive solution overview in Markdown format...",
  "suggestedOemSolutions": ["OEM Solution 1", "OEM Solution 2", "OEM Solution 3"],
  "proposedArchitecture": "High-level architecture description...",
  "technologyStack": ["Technology 1", "Technology 2", "Technology 3"],
  "keyFeatures": ["Feature 1", "Feature 2", "Feature 3"],
  "securityConsiderations": ["Security measure 1", "Security measure 2", "Security measure 3"],
  "scalabilityApproach": "Scalability strategy description...",
  "integrationStrategy": "Integration approach description..."
}

**MANDATORY FIELDS - DO NOT OMIT ANY:**
1. solutionOverview (string)
2. suggestedOemSolutions (array)
3. proposedArchitecture (string)
4. technologyStack (array)
5. keyFeatures (array)
6. securityConsiderations (array)
7. scalabilityApproach (string)
8. integrationStrategy (string)

**For the solutionOverview field:**
- Format as comprehensive Markdown with these sub-sections:
  - **Solution Overview**
- Aim for 2000-2500 words total for comprehensive coverage
- Integrate web research findings throughout

**For structured output fields:**
- Extract specific, actionable items for each field
- These will be used by the Technical Implementation Agent
- Be specific and technical in your extractions
`,
  config: {
    safetySettings: [
      {
        category: 'HARM_CATEGORY_DANGEROUS_CONTENT',
        threshold: 'BLOCK_NONE',
      },
      {
        category: 'HARM_CATEGORY_HARASSMENT',
        threshold: 'BLOCK_MEDIUM_AND_ABOVE',
      },
      {
        category: 'HARM_CATEGORY_HATE_SPEECH',
        threshold: 'BLOCK_MEDIUM_AND_ABOVE',
      },
    ],
  },
});

const solutionSynthesizerFlow = ai.defineFlow(
  {
    name: 'solutionSynthesizerFlow',
    inputSchema: SolutionOverviewInputSchema,
    outputSchema: SolutionOverviewOutputSchema,
  },
  async input => {
    /**
     * Processes the AI output to ensure it's valid JSON and properly formatted
     */
    const processAiOutput = (rawOutput: any): SolutionOverviewOutput => {
      if (!rawOutput) {
        throw new Error('No output received from AI');
      }

      // If it's already a parsed object with the expected structure
      if (typeof rawOutput === 'object' && rawOutput.solutionOverview) {
        return rawOutput as SolutionOverviewOutput;
      }

      // If it's a string that might be JSON
      if (typeof rawOutput === 'string') {
        try {
          return safeJsonParse(rawOutput);
        } catch (e) {
          console.warn('Failed to parse AI output as JSON, treating as raw string');
          // If it's just a string, wrap it in the expected structure
          return {
            solutionOverview: rawOutput,
            suggestedOemSolutions: [],
            proposedArchitecture: '',
            technologyStack: [],
            keyFeatures: [],
            securityConsiderations: [],
            scalabilityApproach: '',
            integrationStrategy: ''
          };
        }
      }

      // If we get here, we don't know how to handle the output
      throw new Error('Unexpected AI output format');
    };
    try {
      console.log('Solution Synthesizer Input:', JSON.stringify(input, null, 2));

      let output;
      try {
        const result = await solutionSynthesizerPrompt(input);
        // Process the output to ensure it's valid JSON
        let processedAiOutput = processAiOutput(result.output);
        if (processedAiOutput && processedAiOutput.solutionOverview) {
          processedAiOutput.solutionOverview = await enhanceMarkdownWithMermaidImages(processedAiOutput.solutionOverview);
        }
        output = processedAiOutput;
      } catch (promptError) {
        console.error('Error from solution synthesizer prompt:', promptError);
        // Check if it's a schema validation error
        if (promptError instanceof Error && 
            (promptError.message.includes('Schema validation failed') || 
             promptError.message.includes('Failed to parse JSON'))) {
          console.warn('Schema validation or JSON parsing failed, using fallback response');
          output = null;
        } else {
          output = null;
        }
      }

      if (!output) {
        console.warn('AI failed to generate solution overview, creating fallback response');

        // Create a fallback response
        let tempFallbackOutput: SolutionOverviewOutput = {
          solutionOverview: `# Solution Overview

## Executive Summary
[A detailed solution overview will be generated based on the RFP requirements. If AI generation failed, please review inputs or consult a solution architect for details. This section typically outlines the proposed solution, its benefits, and alignment with your needs.]

## Proposed Architecture
[The proposed architecture will be designed to meet your specific needs, focusing on key architectural drivers such as scalability, security, and integration. This section would typically describe the high-level system design.]

\`\`\`mermaid
graph TD;
    A[Client] --> B{Load Balancer};
    B --> C[Web Server 1];
    B --> D[Web Server 2];
\`\`\`

## Technology Stack
- [Core Platform/Technology 1]
- [Supporting Technology/Framework 1]
- [Data Storage Solution]
- [Integration Technologies]

## Key Features
- [Key Feature 1 based on RFP]
- [Key Feature 2 based on RFP]
- [Key Feature 3 based on RFP]

## Security Considerations
- [Security Measure 1]
- [Data Privacy Approach]

## Scalability Approach
[The approach to ensure the solution can scale according to demand will be detailed here.]

## Integration Strategy
[The strategy for integrating with existing systems and any necessary third-party services will be outlined here.]`,
          suggestedOemSolutions: [
            "[Suggested OEM Solution 1]",
            "[Suggested OEM Solution 2]"
          ],
          proposedArchitecture: "[High-level description of the proposed architecture based on RFP requirements]",
          technologyStack: [
            "[Core Platform/Technology]",
            "[Key Framework 1]",
            "[Database Technology]",
            "[API/Integration Technology]"
          ],
          keyFeatures: [
            "Customer Visit Scheduling",
            "Lead Management System",
            "Comprehensive Reporting",
            "Mobile Application",
            "ERP Integration",
            "Role-Based Security"
          ],
          securityConsiderations: [
            "End-to-end encryption (TLS 1.3)",
            "Role-based access control",
            "Secure API authentication",
            "Data privacy compliance",
            "Audit trails and logging"
          ],
          scalabilityApproach: "[Description of scalability approach, e.g., Cloud-native auto-scaling, load balancing strategies]",
          integrationStrategy: "[Description of integration strategy, e.g., API-first, event-driven, specific middleware]"
        };

        // Process the mermaid diagrams in the fallback solution overview
        if (tempFallbackOutput.solutionOverview) {
          tempFallbackOutput.solutionOverview = await enhanceMarkdownWithMermaidImages(tempFallbackOutput.solutionOverview);
        }
        output = tempFallbackOutput; // Assign to output to continue the flow
      }

      // Validate required fields and add missing ones
      if (!output.solutionOverview || typeof output.solutionOverview !== 'string') {
        console.warn('AI output missing solutionOverview field, using fallback');
        output.solutionOverview = "# Solution Overview\n\nComprehensive CRM solution using OutSystems platform with integrated reporting and mobile capabilities.";
      }

      if (!output.suggestedOemSolutions || !Array.isArray(output.suggestedOemSolutions)) {
        console.warn('AI output missing suggestedOemSolutions field, adding fallback');
        output.suggestedOemSolutions = [
          "OutSystems Platform",
          "Microsoft Power BI",
          "Microsoft Graph API",
          "Progress Database Connectors"
        ];
      }

      if (!output.proposedArchitecture || typeof output.proposedArchitecture !== 'string') {
        console.warn('AI output missing proposedArchitecture field, adding fallback');
        output.proposedArchitecture = "Cloud-native architecture using OutSystems with secure integration capabilities";
      }

      if (!output.technologyStack || !Array.isArray(output.technologyStack)) {
        console.warn('AI output missing technologyStack field, adding fallback');
        output.technologyStack = ["OutSystems", "Microsoft Power BI", "RESTful APIs", "SQL Database"];
      }

      if (!output.keyFeatures || !Array.isArray(output.keyFeatures)) {
        console.warn('AI output missing keyFeatures field, adding fallback');
        output.keyFeatures = ["Customer Management", "Visit Scheduling", "Reporting", "Mobile Access"];
      }

      if (!output.securityConsiderations || !Array.isArray(output.securityConsiderations)) {
        console.warn('AI output missing securityConsiderations field, adding fallback');
        output.securityConsiderations = ["Role-based access", "Data encryption", "Secure APIs"];
      }

      if (!output.scalabilityApproach || typeof output.scalabilityApproach !== 'string') {
        console.warn('AI output missing scalabilityApproach field, adding fallback');
        output.scalabilityApproach = "Cloud-native scaling with OutSystems platform capabilities";
      }

      if (!output.integrationStrategy || typeof output.integrationStrategy !== 'string') {
        console.warn('AI output missing integrationStrategy field, adding fallback');
        output.integrationStrategy = "API-first integration with secure connectivity to on-premise systems";
      }

      return output;
    } catch (error) {
      console.error('Error in solution synthesizer flow:', error);
      throw error;
    }
  }
);
