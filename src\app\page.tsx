"use client";

import { useEffect, lazy, Suspense } from 'react';
import type React from 'react';
import { AppHeader } from '@/components/AppHeader';
import { useToast } from '@/hooks/use-toast';
import { useProposalState } from '@/hooks/useProposalState';
import type { AnalyzeRequirementsInput } from '@/ai/flows/requirements-analyzer';
import type { ISuggestion } from '@/types/proposal';
import { analyzeRequirementsAction, generateSolutionOverviewAction, enhanceProposalAction, generateArchitectureDiagramAction, generateComprehensiveProposalAction } from './actions';
import { Loader2 } from 'lucide-react';
import { Separator } from '@/components/ui/separator';
import { logServerError } from '@/lib/error-utils';

// Import modular components
import { RFPInputSection } from '@/components/proposal/RFPInputSection';
import { RequirementsDisplaySection } from '@/components/proposal/RequirementsDisplaySection';
import { UserPreferencesSection } from '@/components/proposal/UserPreferencesSection';
import { SolutionScopeSection } from '@/components/proposal/SolutionScopeSection';
import { ArchitectureDiagramSection } from '@/components/proposal/ArchitectureDiagramSection';
import { EnhancementSection } from '@/components/proposal/EnhancementSection';
import { ExportSection } from '@/components/proposal/ExportSection';

// Lazy load heavy components
const ComprehensiveProposalSection = lazy(() => import('@/components/proposal/ComprehensiveProposalSection').then(module => ({ default: module.ComprehensiveProposalSection })));

// Helper function to build the draft proposal markdown
const buildDraftProposalMarkdown = (
  understandingReqs: string,
  proposalText: string,
  oemSolutions: string[],
  technicalImplementation: string,
  diagramSection: string,
  appliedSuggestions: ISuggestion[],
  enhancementSuggestions: string[]
): string => {
  return `
# Proposal Draft

## Understanding the Requirements
${understandingReqs || "No content generated yet."}

## Solution Overview
${proposalText || "No content generated yet."}

### Suggested OEM Solutions
${oemSolutions.length > 0 ? oemSolutions.map(s => `- ${s}`).join('\n') : "No OEM solutions suggested yet."}

${technicalImplementation ? `## Technical Implementation\n\n${technicalImplementation}\n\n` : ''}
${diagramSection}

## Applied Suggestions
${appliedSuggestions.length > 0
  ? appliedSuggestions.map((s, i) => `${i + 1}. ${s.text}`).join('\n\n')
  : "No suggestions have been applied yet."}

## Enhancement Suggestions (Internal Use)
${enhancementSuggestions.length > 0
  ? enhancementSuggestions.map(s => `- ${s}`).join('\n')
  : "No enhancement suggestions generated yet."}
  `.trim();
};

export default function ProposalPilotPage() {
  const { toast } = useToast();
  const { state, updateState, buildUserPreferences } = useProposalState();

  // Handler to update both currentProposalText and comprehensiveProposal after amendment
  const handleAmendedProposalTextUpdate = (newText: string, isComprehensiveEdit: boolean = false) => {
    updateState({ currentProposalText: newText });
    // Only update comprehensiveProposal if this edit is intended for it
    // or if we're editing the comprehensive proposal directly
    if (isComprehensiveEdit || (state.comprehensiveProposal && state.currentProposalText === state.comprehensiveProposal)) {
      updateState({ comprehensiveProposal: newText });
    }
  };

  const handleRfpContentChange = (content: string) => {
    updateState({ rfpContent: content });
    if (state.rfpPdfDataUri) {
      updateState({ rfpPdfDataUri: null, uploadedFileName: null });
      toast({ title: "Input Switched", description: "Switched to manual text input. Uploaded PDF cleared." });
    }
  };

  useEffect(() => {
    if (state.solutionOverview) {
      // Prefill architectureInput only if it's empty, to preserve user edits
      if (!state.architectureInput.trim()) {
        updateState({ architectureInput: state.solutionOverview });
      }

      // Initialize or update currentProposalText when solutionOverview changes
      if (!state.currentProposalText) {
        updateState({ currentProposalText: state.solutionOverview });
      }
    }
  }, [state.solutionOverview, state.architectureInput, state.currentProposalText, updateState]);

  useEffect(() => {
    if (state.enhancementSuggestions && state.enhancementSuggestions.length > 0) {
      const mappedSuggestions: ISuggestion[] = state.enhancementSuggestions.map((text, index) => ({
        id: `enh-sug-${Date.now()}-${index}`,
        text,
        type: 'general' // Default type, can be refined if suggestion source provides more info
      }));
      updateState({ amendmentInitialSuggestions: mappedSuggestions });
    } else {
      updateState({ amendmentInitialSuggestions: [] }); // Clear if no suggestions
    }
  }, [state.enhancementSuggestions, updateState]);

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();

    reader.onload = (e) => {
      const result = e.target?.result as string;
      if (file.type === "application/pdf") {
        updateState({
          rfpPdfDataUri: result,
          rfpContent: '',
          uploadedFileName: file.name
        });
        toast({ title: "PDF Uploaded", description: `${file.name} ready for analysis.` });
      } else if (file.type === "text/plain" || file.type === "text/markdown") {
        updateState({
          rfpContent: result,
          rfpPdfDataUri: null,
          uploadedFileName: file.name
        });
        toast({ title: "Text File Loaded", description: `Content from ${file.name} loaded into textarea.` });
      } else {
        updateState({
          rfpContent: '',
          rfpPdfDataUri: null,
          uploadedFileName: null
        });
        toast({
          title: "File Type Not Supported for Direct Parsing",
          description: `${file.name} selected. Please use .txt, .md, or .pdf. For other formats, copy/paste text.`,
          variant: "destructive",
          duration: 7000,
        });
      }
    };

    reader.onerror = () => {
      updateState({
        rfpContent: '',
        rfpPdfDataUri: null,
        uploadedFileName: null
      });
      toast({ title: "File Read Error", description: "Failed to read the file.", variant: "destructive" });
    };

    if (file.type === "application/pdf" || file.type === "text/plain" || file.type === "text/markdown") {
       if (file.type === "application/pdf") {
        reader.readAsDataURL(file);
      } else {
        reader.readAsText(file);
      }
    } else {
        updateState({
          rfpContent: '',
          rfpPdfDataUri: null,
          uploadedFileName: null
        });
        toast({
          title: "File Type Not Supported",
          description: `${file.name} selected. Please upload a .txt, .md, or .pdf file.`,
          variant: "destructive",
          duration: 7000,
        });
    }
    event.target.value = '';
  };

  const clearUploadedFile = () => {
    updateState({ rfpPdfDataUri: null, uploadedFileName: null });
    toast({ title: "Upload Cleared", description: "Uploaded file has been removed." });
  };

  const handleAnalyzeRequirements = async () => {
    let analysisInput: AnalyzeRequirementsInput | null = null;

    if (state.rfpPdfDataUri) {
      analysisInput = { inputType: 'pdf', rfpPdfDataUri: state.rfpPdfDataUri };
    } else if (state.rfpContent.trim()) {
      analysisInput = { inputType: 'text', rfpContent: state.rfpContent.trim() };
    }

    if (!analysisInput) {
      toast({ title: "Input Missing", description: "Please provide RFP content by typing or uploading a .txt, .md, or .pdf file.", variant: "destructive" });
      return;
    }

    updateState({
      understandingReqsAttempted: true,
      isLoadingRequirements: true,
      understandingReqs: ''
    });

    try {
      const result = await analyzeRequirementsAction(analysisInput);
      updateState({ understandingReqs: result.understandingOfTheRequirements });
      toast({ title: "Success", description: "Requirements analyzed." });
    } catch (error: any) {
      toast({ title: "Error Analyzing Requirements", description: error.message || "Failed to analyze requirements.", variant: "destructive" });
    } finally {
      updateState({ isLoadingRequirements: false });
    }
  };

  const handleSynthesizeSolution = async () => {
    if (!state.solutionKeyPoints.trim()) {
      toast({ title: "Input Missing", description: "Please provide key RFP requirements for the solution.", variant: "destructive" });
      return;
    }

    updateState({
      solutionOverviewAttempted: true,
      isLoadingSolution: true,
      solutionOverview: '',
      oemSolutions: []
    });

    try {
      // First try without fallback mode
      const result = await generateSolutionOverviewAction({
        rfpRequirements: state.solutionKeyPoints,
        _fallbackMode: false
      });
      updateState({
        solutionOverview: result.solutionOverview,
        oemSolutions: result.suggestedOemSolutions || []
      });
      toast({ title: "Success", description: "Solution overview generated." });
    } catch (error: any) {
      logServerError(error, 'Error in solution overview generation');

      // If first attempt fails, try with fallback mode
      try {
        toast({
          title: "Retrying with fallback mode",
          description: "Encountered an issue with web research, falling back to local knowledge."
        });

        const fallbackResult = await generateSolutionOverviewAction({
          rfpRequirements: state.solutionKeyPoints,
          _fallbackMode: true
        });

        updateState({
          solutionOverview: fallbackResult.solutionOverview,
          oemSolutions: fallbackResult.suggestedOemSolutions || []
        });
        toast({
          title: "Success",
          description: "Solution overview generated using fallback mode (no web research)."
        });
      } catch (fallbackError: any) {
        logServerError(fallbackError, 'Fallback solution generation failed');
        toast({
          title: "Error",
          description: fallbackError.message || "Failed to generate solution overview in fallback mode.",
          variant: "destructive"
        });
      }
    } finally {
      updateState({ isLoadingSolution: false });
    }
  };

  const handleEnhanceProposal = async () => {
    if (!state.understandingReqs.trim() && !state.solutionOverview.trim()) {
      toast({ title: "Input Missing", description: "Please generate 'Understanding Requirements' and 'Solution Overview' sections first.", variant: "destructive" });
      return;
    }

    updateState({ isLoadingEnhancements: true, enhancementSuggestions: [] });

    try {
      const result = await enhanceProposalAction({
        understandingRequirements: state.understandingReqs,
        solutionOverview: state.solutionOverview,
      });
      updateState({ enhancementSuggestions: result.recommendations || [] });
      toast({ title: "Success", description: "Enhancement suggestions provided." });
    } catch (error: any) {
      toast({ title: "Error", description: error.message || "Failed to get enhancement suggestions.", variant: "destructive" });
    } finally {
      updateState({ isLoadingEnhancements: false });
    }
  };

  const handleGenerateDiagram = async () => {
    if (!state.architectureInput.trim()) {
      toast({ title: "Input Missing", description: "Please provide a description for the architecture diagram.", variant: "destructive" });
      return;
    }

    updateState({ isLoadingDiagram: true });

    try {
      const result = await generateArchitectureDiagramAction({
        architectureDescription: state.architectureInput,
        diagramType: state.diagramType,
      });
      updateState({ mermaidScript: result.mermaidScript });
      toast({ title: "Success", description: "Architecture diagram generated." });
    } catch (error: any) {
      toast({ title: "Error Generating Diagram", description: error.message || "Failed to generate diagram.", variant: "destructive" });
    } finally {
      updateState({ isLoadingDiagram: false });
    }
  };

  const handleGenerateComprehensiveProposal = async () => {
    let analysisInput: AnalyzeRequirementsInput | null = null;

    if (state.rfpPdfDataUri) {
      analysisInput = { inputType: 'pdf', rfpPdfDataUri: state.rfpPdfDataUri };
    } else if (state.rfpContent.trim()) {
      analysisInput = { inputType: 'text', rfpContent: state.rfpContent.trim() };
    }

    if (!analysisInput) {
      toast({ title: "Input Missing", description: "Please provide RFP content by typing or uploading a .txt, .md, or .pdf file.", variant: "destructive" });
      return;
    }

    updateState({
      isLoadingComprehensive: true,
      comprehensiveProgress: 0,
      comprehensiveCurrentStep: 'Initializing...',
      comprehensiveProposal: '',
      technicalImplementation: ''
    });

    // Simulate progress updates
    let progressInterval: ReturnType<typeof setInterval>;
    let stepInterval: ReturnType<typeof setInterval>;

    progressInterval = setInterval(() => {
      if (state.comprehensiveProgress < 90) {
        const increment = Math.random() * 10 + 5;
        updateState({
          comprehensiveProgress: Math.min(state.comprehensiveProgress + increment, 90)
        });
      }
    }, 1000);

    // Update step names based on progress
    stepInterval = setInterval(() => {
      let newStep = state.comprehensiveCurrentStep;
      if (state.comprehensiveProgress < 30) {
        newStep = 'Analyzing Requirements...';
      } else if (state.comprehensiveProgress < 60) {
        newStep = 'Generating Solution Overview...';
      } else if (state.comprehensiveProgress < 90) {
        newStep = 'Creating Technical Implementation...';
      } else {
        newStep = 'Finalizing Proposal...';
      }
      updateState({ comprehensiveCurrentStep: newStep });
    }, 2000);

    try {
      const userPreferences = buildUserPreferences();
      const result = await generateComprehensiveProposalAction({
        inputType: analysisInput.inputType,
        rfpContent: analysisInput.inputType === 'text' ? analysisInput.rfpContent : undefined,
        rfpPdfDataUri: analysisInput.inputType === 'pdf' ? analysisInput.rfpPdfDataUri : undefined,
        generateRequirements: true,
        generateSolution: true,
        generateTechnical: true,
        userPreferences,
      });

      // Clear intervals and set to 100%
      clearInterval(progressInterval);
      clearInterval(stepInterval);

      const updates: any = {
        comprehensiveProgress: 100,
        comprehensiveCurrentStep: 'Complete!',
        comprehensiveProposal: result.completeProposal
      };

      // Update individual sections for compatibility with existing UI
      if (result.requirementsAnalysis) {
        updates.understandingReqs = result.requirementsAnalysis.understandingOfTheRequirements;
        updates.understandingReqsAttempted = true;
      }

      if (result.solutionOverview) {
        updates.solutionOverview = result.solutionOverview.solutionOverview;
        updates.oemSolutions = result.solutionOverview.suggestedOemSolutions || [];
        updates.solutionOverviewAttempted = true;
      }

      if (result.technicalImplementation) {
        updates.technicalImplementation = result.technicalImplementation.technicalImplementation;
      }

      updateState(updates);

      toast({
        title: "Success",
        description: `Comprehensive proposal generated! ${result.generationMetadata.estimatedWordCount} words across ${result.generationMetadata.generatedSections} sections in ${result.generationMetadata.generationTime}s.`
      });

    } catch (error: any) {
      // Clear intervals on error
      clearInterval(progressInterval);
      clearInterval(stepInterval);
      toast({ title: "Error", description: error.message || "Failed to generate comprehensive proposal.", variant: "destructive" });
    } finally {
      updateState({
        isLoadingComprehensive: false,
        comprehensiveProgress: 0,
        comprehensiveCurrentStep: ''
      });
    }
  };

  const handleExportProposal = () => {
    // Use comprehensive proposal if available, otherwise fall back to individual sections
    let markdownContent = '';

    // Build diagram section once to reuse in both branches
    let diagramSection = '';
    if (state.mermaidSvg) {
      try {
        // Convert SVG content to data URL for proper markdown image embedding
        const svgDataUrl = `data:image/svg+xml;base64,${btoa(unescape(encodeURIComponent(state.mermaidSvg)))}`;
        diagramSection = `
## Architecture Diagram

![Architecture Diagram](${svgDataUrl})
`;
      } catch (error) {
        console.error('Error converting SVG to data URL:', error);
        // Fallback to mermaid script if SVG conversion fails
        diagramSection = `
## Architecture Diagram

\`\`\`mermaid
${state.mermaidScript}
\`\`\`
`;
        toast({
          title: 'SVG Export Warning',
          description: 'Could not export diagram as image. Using Mermaid script instead.',
          variant: 'destructive',
        });
      }
    } else if (state.mermaidScript) {
      diagramSection = `
## Architecture Diagram

\`\`\`mermaid
${state.mermaidScript}
\`\`\`
`;
    }

    if (state.comprehensiveProposal) {
      // Append diagram section to the comprehensive proposal
      markdownContent = `${state.comprehensiveProposal.trim()}
${diagramSection}`;
    } else {
      // Use the currentProposalText if available, otherwise fall back to solutionOverview
      const proposalTextForDraft = state.currentProposalText || state.solutionOverview;
      markdownContent = buildDraftProposalMarkdown(
        state.understandingReqs,
        proposalTextForDraft,
        state.oemSolutions,
        state.technicalImplementation,
        diagramSection,
        state.appliedSuggestions,
        state.enhancementSuggestions
      );
    }

    const blob = new Blob([markdownContent.trim()], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'ProposalPilot_Comprehensive.md';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    // Show appropriate success message based on what was exported
    const description = state.comprehensiveProposal
      ? "Comprehensive proposal downloaded as Markdown."
      : "Proposal draft downloaded as Markdown.";
    toast({ title: "Exported", description });
  };


  return (
    <div className="min-h-screen bg-background">
      <AppHeader />
      <main className="container mx-auto px-4 py-8 md:p-8 space-y-8 flex-grow">

        {/* RFP Input Section */}
        <RFPInputSection
          rfpContent={state.rfpContent}
          rfpPdfDataUri={state.rfpPdfDataUri}
          uploadedFileName={state.uploadedFileName}
          isLoadingRequirements={state.isLoadingRequirements}
          onRfpContentChange={handleRfpContentChange}
          onFileUpload={handleFileUpload}
          onClearUploadedFile={clearUploadedFile}
          onAnalyzeRequirements={handleAnalyzeRequirements}
        />

        {/* Requirements Display Section */}
        <RequirementsDisplaySection
          understandingReqs={state.understandingReqs}
          requirementsAttempted={state.understandingReqsAttempted}
          isLoadingRequirements={state.isLoadingRequirements}
          onUnderstandingReqsChange={(value) => updateState({ understandingReqs: value })}
        />

        {/* User Preferences Section */}
        <UserPreferencesSection
          preferredTechnologies={state.preferredTechnologies}
          technicalConstraints={state.technicalConstraints}
          budgetConsiderations={state.budgetConsiderations}
          timelineConstraints={state.timelineConstraints}
          complianceRequirements={state.complianceRequirements}
          integrationRequirements={state.integrationRequirements}
          scalabilityRequirements={state.scalabilityRequirements}
          securityRequirements={state.securityRequirements}
          additionalConsiderations={state.additionalConsiderations}
          onPreferredTechnologiesChange={(value) => updateState({ preferredTechnologies: value })}
          onTechnicalConstraintsChange={(value) => updateState({ technicalConstraints: value })}
          onBudgetConsiderationsChange={(value) => updateState({ budgetConsiderations: value })}
          onTimelineConstraintsChange={(value) => updateState({ timelineConstraints: value })}
          onComplianceRequirementsChange={(value) => updateState({ complianceRequirements: value })}
          onIntegrationRequirementsChange={(value) => updateState({ integrationRequirements: value })}
          onScalabilityRequirementsChange={(value) => updateState({ scalabilityRequirements: value })}
          onSecurityRequirementsChange={(value) => updateState({ securityRequirements: value })}
          onAdditionalConsiderationsChange={(value) => updateState({ additionalConsiderations: value })}
        />

        {/* Comprehensive Proposal Section */}
        <Suspense fallback={
          <div className="flex items-center justify-center p-8">
            <Loader2 className="h-6 w-6 animate-spin" />
            <span className="ml-2">Loading comprehensive proposal section...</span>
          </div>
        }>
          <ComprehensiveProposalSection
            isLoadingComprehensive={state.isLoadingComprehensive}
            comprehensiveProgress={state.comprehensiveProgress}
            comprehensiveCurrentStep={state.comprehensiveCurrentStep}
            comprehensiveProposal={state.comprehensiveProposal}
            technicalImplementation={state.technicalImplementation}
            showComprehensiveMode={state.showComprehensiveMode}
            rfpContent={state.rfpContent}
            rfpPdfDataUri={state.rfpPdfDataUri}
            onGenerateComprehensiveProposal={handleGenerateComprehensiveProposal}
            onToggleComprehensiveMode={() => updateState({ showComprehensiveMode: !state.showComprehensiveMode })}
            onComprehensiveProposalChange={(value) => updateState({ comprehensiveProposal: value })}
            onTechnicalImplementationChange={(value) => updateState({ technicalImplementation: value })}
          />
        </Suspense>

        <Separator className="my-8" />

        {/* Solution Scope Section */}
        <SolutionScopeSection
          solutionKeyPoints={state.solutionKeyPoints}
          solutionOverview={state.solutionOverview}
          oemSolutions={state.oemSolutions}
          solutionOverviewAttempted={state.solutionOverviewAttempted}
          isLoadingSolution={state.isLoadingSolution}
          onSolutionKeyPointsChange={(value) => updateState({ solutionKeyPoints: value })}
          onSolutionOverviewChange={(value) => updateState({ solutionOverview: value })}
          onSynthesizeSolution={handleSynthesizeSolution}
        />

        <Separator className="my-8" />

        {/* Architecture Diagram Section */}
        <ArchitectureDiagramSection
          architectureInput={state.architectureInput}
          diagramType={state.diagramType}
          mermaidScript={state.mermaidScript}
          mermaidSvg={state.mermaidSvg}
          isLoadingDiagram={state.isLoadingDiagram}
          onArchitectureInputChange={(value) => updateState({ architectureInput: value })}
          onDiagramTypeChange={(value) => updateState({ diagramType: value })}
          onMermaidScriptChange={(value) => updateState({ mermaidScript: value })}
          onMermaidSvgChange={(value) => updateState({ mermaidSvg: value })}
          onGenerateDiagram={handleGenerateDiagram}
        />

        <Separator className="my-8" />

        {/* Enhancement Section */}
        <EnhancementSection
          enhancementSuggestions={state.enhancementSuggestions}
          amendmentInitialSuggestions={state.amendmentInitialSuggestions}
          appliedSuggestions={state.appliedSuggestions}
          currentProposalText={state.currentProposalText}
          isLoadingEnhancements={state.isLoadingEnhancements}
          onEnhanceProposal={handleEnhanceProposal}
          onCurrentProposalTextChange={(value) => updateState({ currentProposalText: value })}
          onApplySuggestion={(suggestion) => updateState({ appliedSuggestions: [...state.appliedSuggestions, suggestion] })}
        />

        <Separator className="my-8" />

        {/* Export Section */}
        <ExportSection
          understandingReqs={state.understandingReqs}
          solutionOverview={state.solutionOverview}
          oemSolutions={state.oemSolutions}
          technicalImplementation={state.technicalImplementation}
          comprehensiveProposal={state.comprehensiveProposal}
          appliedSuggestions={state.appliedSuggestions}
          mermaidSvg={state.mermaidSvg}
          onExportMarkdown={handleExportProposal}
        />

      </main>
      <footer className="text-center p-4 text-sm text-muted-foreground border-t">
        ProposalPilot &copy; {new Date().getFullYear()}
      </footer>
    </div>
  );
}
