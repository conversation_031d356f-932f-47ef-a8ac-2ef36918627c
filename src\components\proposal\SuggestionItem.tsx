"use client";

import React from "react";
import { ISuggestion } from "@/types/proposal";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";

interface SuggestionItemProps {
  suggestion: ISuggestion;
  isSelected: boolean;
  onSelect: (suggestionId: string) => void;
}

const SuggestionItem: React.FC<SuggestionItemProps> = ({
  suggestion,
  isSelected,
  onSelect,
}) => {
  const handleSelectChange = () => {
    onSelect(suggestion.id);
  };

  return (
    <div className="flex items-center space-x-2 p-2 border-b last:border-b-0 hover:bg-muted/50 transition-colors rounded-md">
      <Checkbox
        id={`suggestion-${suggestion.id}`}
        checked={isSelected}
        onCheckedChange={handleSelectChange}
        aria-labelledby={`suggestion-label-${suggestion.id}`}
      />
      <Label 
        htmlFor={`suggestion-${suggestion.id}`} 
        id={`suggestion-label-${suggestion.id}`}
        className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer flex-1"
      >
        {suggestion.text}
      </Label>
    </div>
  );
};

export default SuggestionItem;
