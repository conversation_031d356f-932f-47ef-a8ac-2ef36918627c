# secrets-values.yaml
# This file contains sensitive information and should NOT be committed to version control.
# Add this file to your .gitignore

# API Keys
secrets:
  # Existing API keys
  geminiApiKey: "AIzaSyBWKWSbWhAe3GrSI0ko1MVOKvvRt70UosA"
  braveApiKey: "BSAtPiv3uykgac7s8vuOMdHR3p1f4Un"
  serperApiKey: "eb3de50122fcdd22972a065e4bab9f7b77959c90"
  
  # PostgreSQL Credentials
  # The password for the 'proposalpilot' database user
  postgresPassword: "PsqEK5z0rd"  # This is the 'password' from the secret
  # The password for the 'postgres' admin user
  postgresPostgresPassword: "czrWNlWK9O"  # This is the 'postgres-password' from the secret

# PostgreSQL Configuration
postgresql:
  auth:
    # These will be used to set the database name and user
    database: "proposalpilot"
    username: "proposalpilot"
    # The passwords reference the secrets above
    password: "PsqEK5z0rd"  # This should match the postgresPassword above
    postgresPassword: "czrWNlWK9O"  # This should match the postgresPostgresPassword above