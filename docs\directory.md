# Project Directory Structure

```
src/
├── ai/
│   ├── dev.ts
│   ├── flows/
│   │   ├── amendProposalFlow.ts
│   │   ├── architecture-visualizer.ts
│   │   ├── proposal-enhancer.ts
│   │   ├── proposal-orchestrator.ts
│   │   ├── requirements-analyzer.ts
│   │   ├── solution-synthesizer.ts
│   │   └── technical-implementation.ts
│   ├── genkit.ts
│   ├── schemas/
│   │   ├── amendmentSchemas.ts
│   │   └── user-preferences.ts
│   └── tools/
│       └── web-researcher.ts
│
├── app/
│   ├── actions.ts
│   ├── api/
│   │   ├── generate-advanced-proposal/
│   │   │   └── route.ts
│   │   ├── proposal/
│   │   │   └── amend/
│   │   │       └── route.ts
│   │   └── test-brave-search/
│   │       └── route.ts
│   ├── error.tsx
│   ├── favicon.ico
│   ├── globals.css
│   ├── layout.tsx
│   └── page.tsx
│
├── components/
│   ├── AppHeader.tsx
│   ├── MermaidPreview.tsx
│   ├── header.tsx
│   ├── proposal/
│   │   ├── ProposalAmendmentSection.tsx
│   │   ├── ProposalDisplayCard.tsx
│   │   ├── ProposalRatingControl.tsx
│   │   ├── SuggestionItem.tsx
│   │   └── SuggestionsList.tsx
│   ├── providers/
│   │   └── ReactQueryProvider.tsx
│   ├── research-display.tsx
│   ├── theme-provider.tsx
│   ├── theme-toggle.tsx
│   └── ui/
│       ├── accordion.tsx
│       ├── alert-dialog.tsx
│       ├── alert.tsx
│       ├── avatar.tsx
│       ├── badge.tsx
│       ├── button.tsx
│       ├── calendar.tsx
│       ├── card.tsx
│       ├── chart.tsx
│       ├── checkbox.tsx
│       ├── dialog.tsx
│       ├── dropdown-menu.tsx
│       ├── form.tsx
│       ├── input.tsx
│       ├── label.tsx
│       ├── menubar.tsx
│       ├── popover.tsx
│       ├── progress.tsx
│       ├── radio-group.tsx
│       ├── scroll-area.tsx
│       ├── select.tsx
│       ├── separator.tsx
│       ├── sheet.tsx
│       ├── sidebar.tsx
│       ├── skeleton.tsx
│       ├── slider.tsx
│       ├── switch.tsx
│       ├── table.tsx
│       ├── tabs.tsx
│       ├── textarea.tsx
│       ├── toast.tsx
│       ├── toaster.tsx
│       └── tooltip.tsx
│
├── hooks/
│   ├── use-mobile.tsx
│   └── use-toast.ts
│
├── lib/
│   ├── error-utils.ts
│   ├── errors/
│   │   └── api.errors.ts
│   ├── mcp-client.ts
│   ├── mermaid-utils.ts
│   ├── test-integration.ts
│   ├── unified-search-engine.ts
│   └── utils.ts
│
├── services/
│   └── ai/
│       ├── advancedProposalService.test.ts
│       ├── advancedProposalService.ts
│       ├── braveSearchClient.ts
│       └── geminiClient.ts
│
└── types/
    ├── proposal.ts
    └── proposal.types.ts

````

## Directory Overview

- **ai/**: Contains AI-related functionality including flows and tools
  - **flows/**: AI processing pipelines for different tasks
  - **schemas/**: Schemas for AI-related data
  - **tools/**: Reusable AI tools

- **app/**: Main application code
  - **api/**: API route handlers
  - Next.js pages and layouts

- **components/**: Reusable UI components
  - **ui/**: Base UI components (using shadcn/ui)
  - **proposal/**: Proposal-related components
  - **providers/**: Context providers
  - Application-specific components

- **hooks/**: Custom React hooks
  - **use-mobile.tsx**: Hook for detecting mobile devices
  - **use-toast.ts**: Hook for toast notifications

- **lib/**: Shared utility functions and configurations
  - **errors/**: Custom error definitions and utilities
  - **mcp-client.ts**: MCP client implementation
  - **mermaid-utils.ts**: Utilities for working with Mermaid.js
  - **unified-search-engine.ts**: Search functionality implementation

- **services/**: Contains service layer logic, often interacting with external APIs or orchestrating business logic.
  - **ai/**: AI-related services, such as clients for Gemini, Brave Search, and advanced proposal generation logic.

- **types/**: TypeScript type definitions and interfaces used across the application.
  - **proposal.ts & proposal.types.ts**: Type definitions specific to proposals.



*Updated on 2025-06-10 to match current codebase structure*