# ProposalPilot

AI-assisted RFP proposal generation with real web research capabilities.

## Features

- 📄 **RFP Analysis**: Upload and analyze Request for Proposal documents
- 🧠 **AI-Powered Generation**: Generate comprehensive solution overviews using Google's Gemini AI
- 🔍 **Real Web Research**: Integrated Brave Search for up-to-date market insights
- 📊 **Architecture Diagrams**: Generate Mermaid diagrams for technical solutions
- 💡 **Enhancement Suggestions**: AI-driven proposal improvements
- 📱 **Modern UI**: Clean, responsive interface built with Next.js and Tailwind CSS

## Quick Start

1. **Clone and Install**
   ```bash
   git clone <repository-url>
   cd proposalpilot
   npm install
   ```

2. **Configure Environment**
   ```bash
   cp .env.local.example .env.local
   # Add your API keys to .env.local
   ```

3. **Start Development Server**
   ```bash
   npm run dev
   ```

4. **Open in Browser**
   Navigate to `http://localhost:9002`

## Web Search Integration

ProposalPilot includes real web search capabilities using Brave Search MCP (Model Context Protocol).

### Setup Brave Search

1. Get your free API key from [Brave Search API](https://brave.com/search/api/)
2. Add it to your `.env.local`:
   ```env
   BRAVE_API_KEY=your_api_key_here
   ```
3. Test the integration:
   ```bash
   npm run test:brave-search
   ```

For detailed setup instructions, see [docs/brave-search-setup.md](docs/brave-search-setup.md).

## Technology Stack

- **Frontend**: Next.js 15, React 18, Tailwind CSS
- **AI**: Google Genkit with Gemini 2.5 Flash
- **Web Search**: Brave Search API via Model Context Protocol
- **UI Components**: Radix UI, Lucide Icons
- **Charts**: Recharts, Mermaid diagrams
- **Backend**: Next.js API routes, Firebase integration

## Project Structure

```
src/
├── app/                 # Next.js app router
├── components/          # React components
│   └── ui/             # Reusable UI components
├── ai/                 # AI-related functionality
│   ├── flows/          # AI workflow definitions
│   └── tools/          # AI tools (including web research)
├── lib/                # Utility libraries
│   └── mcp-client.ts   # MCP client for Brave Search
└── hooks/              # React hooks

docs/                   # Documentation
scripts/                # Utility scripts
```

## Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run genkit:dev` - Start Genkit development server
- `npm run test:brave-search` - Test Brave Search integration

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the MIT License.
