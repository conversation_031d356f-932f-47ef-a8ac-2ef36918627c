"use client";

import React from 'react';
import { Button, buttonVariants } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { FileText, Lightbulb, UploadCloud, FileUp, CircleX, Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useToast } from '@/hooks/use-toast';

interface RFPInputSectionProps {
  rfpContent: string;
  rfpPdfDataUri: string | null;
  uploadedFileName: string | null;
  isLoadingRequirements: boolean;
  onRfpContentChange: (content: string) => void;
  onFileUpload: (event: React.ChangeEvent<HTMLInputElement>) => void;
  onClearUploadedFile: () => void;
  onAnalyzeRequirements: () => void;
}

export const RFPInputSection: React.FC<RFPInputSectionProps> = ({
  rfpContent,
  rfpPdfDataUri,
  uploadedFileName,
  isLoadingRequirements,
  onRfpContentChange,
  onFileUpload,
  onClearUploadedFile,
  onAnalyzeRequirements,
}) => {
  const { toast } = useToast();

  const handleRfpTextChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newContent = e.target.value;
    onRfpContentChange(newContent);
    
    // Clear PDF if user starts typing
    if (rfpPdfDataUri && newContent.trim()) {
      toast({ 
        title: "Input Switched", 
        description: "Switched to manual text input. Uploaded PDF cleared." 
      });
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' || e.key === ' ') {
      (document.getElementById('rfpFileUpload') as HTMLInputElement)?.click();
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <FileText className="w-7 h-7 text-primary" />
          <div>
            <h2 className="text-xl font-semibold">1. Input RFP Content</h2>
            <p className="text-sm text-muted-foreground">
              Paste RFP text, or upload a .txt, .md, or .pdf file for analysis.
            </p>
          </div>
        </div>
        {isLoadingRequirements && (
          <Loader2 className="h-5 w-5 animate-spin text-primary" />
        )}
      </div>

      <div className="space-y-2">
        <Label htmlFor="rfpContent" className="sr-only">
          RFP Content Area
        </Label>
        <Textarea
          id="rfpContent"
          value={rfpContent}
          onChange={handleRfpTextChange}
          placeholder={
            rfpPdfDataUri 
              ? "PDF uploaded. Text input disabled." 
              : "Paste RFP content here, or use 'Upload File' for .txt, .md, .pdf."
          }
          className="min-h-[200px] text-sm"
          aria-label="RFP Content Input"
          disabled={!!rfpPdfDataUri}
        />
        
        <div className="pt-2 flex items-center gap-2">
          <Label
            htmlFor="rfpFileUpload"
            className={cn(
              buttonVariants({ variant: "outline" }),
              "cursor-pointer"
            )}
            role="button"
            tabIndex={0}
            onKeyDown={handleKeyDown}
          >
            <UploadCloud className="mr-2 h-4 w-4" />
            Upload File (.txt, .md, .pdf)
          </Label>
          <Input
            id="rfpFileUpload"
            type="file"
            accept=".txt,.md,.pdf,text/plain,text/markdown,application/pdf"
            onChange={onFileUpload}
            className="hidden"
          />
          
          {uploadedFileName && (
            <div className="flex items-center gap-2 text-sm p-2 border rounded-md bg-muted/50">
              <FileUp className="h-4 w-4 text-green-600" />
              <span>{uploadedFileName}</span>
              <Button 
                variant="ghost" 
                size="icon" 
                onClick={onClearUploadedFile} 
                className="h-6 w-6"
              >
                <CircleX className="h-4 w-4 text-muted-foreground hover:text-destructive" />
              </Button>
            </div>
          )}
        </div>
        
        {rfpPdfDataUri && (
          <p className="text-xs text-muted-foreground mt-1">
            Note: If you type in the text area above, the uploaded PDF will be cleared.
          </p>
        )}
      </div>

      <div className="pt-4">
        <Button
          onClick={onAnalyzeRequirements}
          disabled={isLoadingRequirements || (!rfpContent.trim() && !rfpPdfDataUri)}
          className="w-full sm:w-auto"
        >
          {isLoadingRequirements ? (
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
          ) : (
            <Lightbulb className="mr-2 h-4 w-4" />
          )}
          Analyze Requirements
        </Button>
      </div>
    </div>
  );
};
