/**
 * Test script for multiple search APIs
 * Run with: node scripts/test-multi-search-apis.js
 */

require('dotenv').config();

async function testSerperAPI(query) {
  const apiKey = process.env.SERPER_API_KEY;
  
  if (!apiKey) {
    console.log('⚠️  SERPER_API_KEY not found - skipping Serper test');
    return null;
  }

  console.log('\n🔍 Testing Serper API...');
  console.log(`📝 API Key: ${apiKey.substring(0, 10)}...`);

  try {
    const response = await fetch('https://google.serper.dev/search', {
      method: 'POST',
      headers: {
        'X-API-KEY': apiKey,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        q: query,
        num: 5,
        gl: 'us',
        hl: 'en',
      }),
    });

    console.log(`📊 Response Status: ${response.status} ${response.statusText}`);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ Serper API Error:', errorText);
      return null;
    }

    const data = await response.json();
    
    console.log('✅ Serper Results:');
    
    // Knowledge Graph
    if (data.knowledgeGraph) {
      console.log(`📚 Knowledge Graph: ${data.knowledgeGraph.title}`);
      console.log(`   ${data.knowledgeGraph.description?.substring(0, 100)}...`);
    }
    
    // Organic results
    if (data.organic) {
      console.log(`📈 Found ${data.organic.length} organic results`);
      data.organic.slice(0, 3).forEach((result, index) => {
        console.log(`\n${index + 1}. ${result.title}`);
        console.log(`   🔗 ${result.link}`);
        console.log(`   📄 ${result.snippet?.substring(0, 100)}...`);
      });
    }

    // People also ask
    if (data.peopleAlsoAsk) {
      console.log(`\n❓ People also ask: ${data.peopleAlsoAsk.length} questions`);
    }

    return data;
  } catch (error) {
    console.error('❌ Serper test failed:', error.message);
    return null;
  }
}

async function testBraveAPI(query) {
  const apiKey = process.env.BRAVE_API_KEY;
  
  if (!apiKey) {
    console.log('⚠️  BRAVE_API_KEY not found - skipping Brave test');
    return null;
  }

  console.log('\n🦁 Testing Brave API...');
  console.log(`📝 API Key: ${apiKey.substring(0, 10)}...`);

  try {
    const params = new URLSearchParams({
      q: query,
      count: '5',
      offset: '0',
      mkt: 'en-US',
      safesearch: 'moderate',
      text_decorations: 'true',
      spellcheck: 'true',
    });

    const response = await fetch(`https://api.search.brave.com/res/v1/web/search?${params}`, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'Accept-Encoding': 'gzip',
        'X-Subscription-Token': apiKey,
      },
    });

    console.log(`📊 Response Status: ${response.status} ${response.statusText}`);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ Brave API Error:', errorText);
      return null;
    }

    const data = await response.json();
    
    console.log('✅ Brave Results:');
    
    if (data.web && data.web.results) {
      console.log(`📈 Found ${data.web.results.length} results`);
      
      data.web.results.slice(0, 3).forEach((result, index) => {
        console.log(`\n${index + 1}. ${result.title}`);
        console.log(`   🔗 ${result.url}`);
        console.log(`   📄 ${result.description?.substring(0, 100)}...`);
      });
    }

    return data;
  } catch (error) {
    console.error('❌ Brave test failed:', error.message);
    return null;
  }
}

async function compareResults(serperData, braveData) {
  console.log('\n📊 COMPARISON ANALYSIS');
  console.log('======================');

  if (!serperData && !braveData) {
    console.log('❌ No data to compare');
    return;
  }

  if (serperData && braveData) {
    const serperUrls = new Set(serperData.organic?.map(r => r.link) || []);
    const braveUrls = new Set(braveData.web?.results?.map(r => r.url) || []);
    
    const commonUrls = [...serperUrls].filter(url => braveUrls.has(url));
    
    console.log(`🔗 Common URLs: ${commonUrls.length}`);
    console.log(`📊 Serper unique: ${serperUrls.size - commonUrls.length}`);
    console.log(`🦁 Brave unique: ${braveUrls.size - commonUrls.length}`);
    
    if (commonUrls.length > 0) {
      console.log('\n🤝 Overlapping results:');
      commonUrls.slice(0, 2).forEach((url, index) => {
        console.log(`${index + 1}. ${url}`);
      });
    }
  }

  console.log('\n💡 Multi-search benefits:');
  console.log('• Redundancy: If one API fails, others continue');
  console.log('• Diversity: Different perspectives and sources');
  console.log('• Quality: Serper for premium, Brave for privacy');
  console.log('• Cost management: Balance free and paid services');
}

async function main() {
  console.log('🚀 Multi-Search API Test');
  console.log('========================');

  const query = 'Next.js 15 new features';
  console.log(`🔍 Test Query: "${query}"`);

  const serperData = await testSerperAPI(query);
  const braveData = await testBraveAPI(query);

  await compareResults(serperData, braveData);

  console.log('\n🎉 Multi-search API test completed!');
  
  if (serperData && braveData) {
    console.log('✅ Both APIs are working - you have full multi-search capability!');
  } else if (serperData || braveData) {
    console.log('⚠️  One API working - partial multi-search capability');
  } else {
    console.log('❌ No APIs working - check your configuration');
  }
}

// Run the test
main().catch(console.error);
