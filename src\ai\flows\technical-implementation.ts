'use server';

/**
 * @fileOverview Generates detailed technical implementation section with development approach, architecture details, and implementation roadmap.
 *
 * - generateTechnicalImplementation - A function that generates the technical implementation section.
 * - TechnicalImplementationInput - The input type for the generateTechnicalImplementation function.
 * - TechnicalImplementationOutput - The return type for the generateTechnicalImplementation function.
 */

import {ai} from '@/ai/genkit';
import {z} from 'genkit';
import {performWebResearch} from '@/ai/tools/web-researcher';
import { UserPreferencesSchema } from '../schemas/user-preferences';

const TechnicalImplementationInputSchema = z.object({
  // Input from Solution Overview Agent
  proposedArchitecture: z.string().describe('High-level architecture description from solution overview.'),
  technologyStack: z.array(z.string()).describe('List of recommended technologies, frameworks, and platforms.'),
  keyFeatures: z.array(z.string()).describe('List of key features and capabilities of the proposed solution.'),
  securityConsiderations: z.array(z.string()).describe('Security measures and considerations for the solution.'),
  scalabilityApproach: z.string().describe('Approach to handling scalability requirements.'),
  integrationStrategy: z.string().describe('Strategy for integrating with existing systems and third-party services.'),

  // Input from Requirements Analysis Agent
  functionalRequirements: z.array(z.string()).describe('List of functional requirements from requirements analysis.'),
  nonFunctionalRequirements: z.array(z.string()).describe('List of non-functional requirements from requirements analysis.'),
  technicalConstraints: z.array(z.string()).describe('Technical constraints from requirements analysis.'),
  integrationRequirements: z.array(z.string()).describe('Integration requirements from requirements analysis.'),
  complianceRequirements: z.array(z.string()).describe('Compliance requirements from requirements analysis.'),

  // User preferences
  userPreferences: UserPreferencesSchema.optional().describe('User preferences and considerations to guide technical implementation'),
});
export type TechnicalImplementationInput = z.infer<typeof TechnicalImplementationInputSchema>;

const TechnicalImplementationOutputSchema = z.object({
  technicalImplementation: z.string().describe('Comprehensive technical implementation section in Markdown format with detailed sub-sections.'),
  developmentMethodology: z.string().describe('Recommended development methodology and approach.'),
  implementationPhases: z.array(z.object({
    phase: z.string().describe('Phase name'),
    duration: z.string().describe('Estimated duration'),
    deliverables: z.array(z.string()).describe('Key deliverables for this phase'),
    milestones: z.array(z.string()).describe('Important milestones in this phase')
  })).describe('Detailed implementation phases with timelines and deliverables.'),
  riskMitigation: z.array(z.object({
    risk: z.string().describe('Identified risk'),
    impact: z.enum(['Low', 'Medium', 'High']).describe('Risk impact level'),
    mitigation: z.string().describe('Mitigation strategy')
  })).describe('Technical risks and mitigation strategies.'),
  qualityAssurance: z.array(z.string()).describe('Quality assurance measures and testing strategies.'),
  performanceOptimization: z.array(z.string()).describe('Performance optimization techniques and strategies.'),
  maintenanceApproach: z.string().describe('Long-term maintenance and support approach.'),
});
export type TechnicalImplementationOutput = z.infer<typeof TechnicalImplementationOutputSchema>;

export async function generateTechnicalImplementation(input: TechnicalImplementationInput): Promise<TechnicalImplementationOutput> {
  return technicalImplementationFlow(input);
}

const technicalImplementationPrompt = ai.definePrompt({
  name: 'technicalImplementationPrompt',
  input: {schema: TechnicalImplementationInputSchema},
  output: {schema: TechnicalImplementationOutputSchema},
  tools: [performWebResearch],
  prompt: `You are a senior technical architect and implementation specialist with expertise in software development, system architecture, and project delivery.
Your task is to generate a comprehensive 'Technical Implementation' section that provides detailed implementation guidance based on the solution overview and requirements analysis.

**Input Data:**

**Proposed Architecture:**
{{{proposedArchitecture}}}

**Technology Stack:**
{{#each technologyStack}}
- {{this}}
{{/each}}

**Key Features:**
{{#each keyFeatures}}
- {{this}}
{{/each}}

**Security Considerations:**
{{#each securityConsiderations}}
- {{this}}
{{/each}}

**Scalability Approach:**
{{{scalabilityApproach}}}

**Integration Strategy:**
{{{integrationStrategy}}}

**Functional Requirements:**
{{#each functionalRequirements}}
- {{this}}
{{/each}}

**Non-Functional Requirements:**
{{#each nonFunctionalRequirements}}
- {{this}}
{{/each}}

**Technical Constraints:**
{{#each technicalConstraints}}
- {{this}}
{{/each}}

**Integration Requirements:**
{{#each integrationRequirements}}
- {{this}}
{{/each}}

**Compliance Requirements:**
{{#each complianceRequirements}}
- {{this}}
{{/each}}

{{#if userPreferences}}
**USER PREFERENCES & CONSIDERATIONS:**
{{#if userPreferences.preferredTechnologies}}
Preferred Technologies: {{#each userPreferences.preferredTechnologies}}{{this}}{{#unless @last}}, {{/unless}}{{/each}}
{{/if}}
{{#if userPreferences.technicalConstraints}}
Technical Constraints: {{#each userPreferences.technicalConstraints}}{{this}}{{#unless @last}}, {{/unless}}{{/each}}
{{/if}}
{{#if userPreferences.budgetConsiderations}}
Budget Considerations: {{userPreferences.budgetConsiderations}}
{{/if}}
{{#if userPreferences.timelineConstraints}}
Timeline Constraints: {{userPreferences.timelineConstraints}}
{{/if}}
{{#if userPreferences.complianceRequirements}}
Additional Compliance Requirements: {{#each userPreferences.complianceRequirements}}{{this}}{{#unless @last}}, {{/unless}}{{/each}}
{{/if}}
{{#if userPreferences.integrationRequirements}}
Additional Integration Requirements: {{#each userPreferences.integrationRequirements}}{{this}}{{#unless @last}}, {{/unless}}{{/each}}
{{/if}}
{{#if userPreferences.scalabilityRequirements}}
Scalability Requirements: {{userPreferences.scalabilityRequirements}}
{{/if}}
{{#if userPreferences.securityRequirements}}
Security Requirements: {{#each userPreferences.securityRequirements}}{{this}}{{#unless @last}}, {{/unless}}{{/each}}
{{/if}}
{{#if userPreferences.additionalConsiderations}}
Additional Considerations: {{userPreferences.additionalConsiderations}}
{{/if}}

**IMPORTANT:** Ensure the technical implementation aligns with user preferences, especially preferred technologies and constraints. Adjust implementation phases and timelines based on budget and timeline constraints.
{{/if}}

**Instructions for Web Research:**
- Research current best practices for the specific technologies in the technology stack
- Look up implementation patterns for the proposed architecture
- Research development methodologies suitable for this type of project
- Find current testing and quality assurance practices for the technology stack
- Research performance optimization techniques for the proposed solution

**CRITICAL INSTRUCTIONS:**
1. You MUST respond with valid JSON only
2. No markdown code blocks or formatting
3. No additional text before or after the JSON
4. Start your response directly with { and end with }

**REQUIRED JSON OUTPUT FORMAT:**
{
  "technicalImplementation": "Your comprehensive technical implementation in Markdown format...",
  "developmentMethodology": "Recommended development methodology...",
  "implementationPhases": [
    {
      "phase": "Phase 1: Foundation",
      "duration": "4-6 weeks",
      "deliverables": ["Deliverable 1", "Deliverable 2"],
      "milestones": ["Milestone 1", "Milestone 2"]
    }
  ],
  "riskMitigation": [
    {
      "risk": "Risk description",
      "impact": "High",
      "mitigation": "Mitigation strategy"
    }
  ],
  "qualityAssurance": ["QA measure 1", "QA measure 2"],
  "performanceOptimization": ["Optimization 1", "Optimization 2"],
  "maintenanceApproach": "Long-term maintenance strategy..."
}

**For the technicalImplementation field:**
- Format as comprehensive Markdown with these sub-sections:
  - **Development Methodology & Approach**
  - **Detailed Architecture Implementation**
  - **Technology Stack Implementation Details**
  - **Development Environment & Tools**
  - **Implementation Phases & Timeline**
  - **Quality Assurance & Testing Strategy**
  - **Performance Optimization**
  - **Security Implementation**
  - **Integration Implementation**
  - **Deployment Strategy**
  - **Monitoring & Logging**
  - **Risk Management**
  - **Maintenance & Support**
- Aim for 2500-3000 words total for comprehensive technical coverage
- Include specific technical details, code examples where relevant, and implementation best practices
- Integrate web research findings throughout to show current best practices

**For structured output fields:**
- Provide detailed, actionable information for each field
- Implementation phases should be realistic and well-defined
- Risk mitigation should cover technical, timeline, and resource risks
- Quality assurance should include automated testing, code review, and validation strategies

**EXAMPLE JSON OUTPUT:**
{
  "technicalImplementation": "# Technical Implementation\\n\\n## Development Methodology\\nAgile development approach...",
  "developmentMethodology": "Agile Scrum with 2-week sprints",
  "implementationPhases": [{"phase": "Phase 1", "duration": "4 weeks", "deliverables": ["Setup"], "milestones": ["Environment ready"]}],
  "riskMitigation": [{"risk": "Technical complexity", "impact": "Medium", "mitigation": "Expert consultation"}],
  "qualityAssurance": ["Unit testing", "Integration testing"],
  "performanceOptimization": ["Database optimization", "Caching"],
  "maintenanceApproach": "Proactive monitoring and regular updates"
}
`,
  config: {
    safetySettings: [
      {
        category: 'HARM_CATEGORY_DANGEROUS_CONTENT',
        threshold: 'BLOCK_ONLY_HIGH',
      },
      {
        category: 'HARM_CATEGORY_HARASSMENT',
        threshold: 'BLOCK_MEDIUM_AND_ABOVE',
      },
      {
        category: 'HARM_CATEGORY_HATE_SPEECH',
        threshold: 'BLOCK_MEDIUM_AND_ABOVE',
      },
    ],
  },
});

function createFallbackTechnicalImplementation(input: TechnicalImplementationInput): TechnicalImplementationOutput {
  return {
    technicalImplementation: `# Technical Implementation

## Development Methodology & Approach
Based on the proposed architecture and requirements, we recommend an Agile development methodology with iterative delivery cycles.

## Technology Stack Implementation
The implementation will leverage the following technologies:
${input.technologyStack.map(tech => `- ${tech}`).join('\n')}

## Implementation Phases
The project will be delivered in structured phases to ensure quality and manage risk.

## Quality Assurance & Testing Strategy
Comprehensive testing strategy including unit tests, integration tests, and user acceptance testing.

## Security Implementation
Security measures will be implemented according to industry best practices and compliance requirements.

## Deployment Strategy
Automated deployment pipeline with staging and production environments.

## Monitoring & Maintenance
Continuous monitoring and proactive maintenance approach.`,
    developmentMethodology: "Agile development with 2-week sprints and continuous integration/deployment",
    implementationPhases: [
      {
        phase: "Phase 1: Foundation & Setup",
        duration: "4-6 weeks",
        deliverables: ["Development environment setup", "Core architecture implementation", "Basic security framework"],
        milestones: ["Environment ready", "Core modules deployed", "Security baseline established"]
      },
      {
        phase: "Phase 2: Core Development",
        duration: "8-12 weeks",
        deliverables: ["Core functionality implementation", "Integration with external systems", "Initial testing"],
        milestones: ["Core features complete", "Integrations working", "Testing framework ready"]
      },
      {
        phase: "Phase 3: Testing & Deployment",
        duration: "4-6 weeks",
        deliverables: ["Comprehensive testing", "Performance optimization", "Production deployment"],
        milestones: ["Testing complete", "Performance targets met", "Production ready"]
      }
    ],
    riskMitigation: [
      {
        risk: "Technical complexity",
        impact: "Medium" as const,
        mitigation: "Proof of concept development and expert consultation"
      },
      {
        risk: "Integration challenges",
        impact: "High" as const,
        mitigation: "Early integration testing and API validation"
      }
    ],
    qualityAssurance: [
      "Automated unit testing with 80%+ code coverage",
      "Integration testing for all external interfaces",
      "Performance testing under expected load",
      "Security testing and vulnerability assessment",
      "User acceptance testing with stakeholders"
    ],
    performanceOptimization: [
      "Database query optimization",
      "Caching strategy implementation",
      "Load balancing configuration",
      "Code profiling and optimization"
    ],
    maintenanceApproach: "Proactive monitoring with automated alerts, regular security updates, and quarterly performance reviews"
  };
}

const technicalImplementationFlow = ai.defineFlow(
  {
    name: 'technicalImplementationFlow',
    inputSchema: TechnicalImplementationInputSchema,
    outputSchema: TechnicalImplementationOutputSchema,
  },
  async (input) => {
    const isDebug = process.env.DEBUG === 'true' || process.env.LOG_LEVEL === 'debug';
    
    try {
      if (isDebug) {
        console.log('[TechnicalImplementation] Processing technical implementation request');
        console.debug('[TechnicalImplementation] Input:', JSON.stringify(input, null, 2));
      }

      let output;
      try {
        const result = await technicalImplementationPrompt(input);
        output = result.output;
      } catch (promptError) {
        console.error('[TechnicalImplementation] Error from technical implementation prompt:', promptError instanceof Error ? promptError.message : String(promptError));
        // If the prompt fails, we'll create a fallback response
        output = null;
      }

      if (isDebug && output) {
        console.debug('[TechnicalImplementation] Successfully generated technical implementation');
      }

      try {
        // If output is null (from prompt error), use fallback
        if (!output) {
          console.warn('[TechnicalImplementation] No output generated, falling back to default implementation');
          return createFallbackTechnicalImplementation(input);
        }

        // Validate the output against the schema
        const validationResult = TechnicalImplementationOutputSchema.safeParse(output);
        
        if (!validationResult.success) {
          console.warn('AI generated invalid technical implementation:', validationResult.error);
          console.warn('Creating fallback response');
          return createFallbackTechnicalImplementation(input);
        }

        // If we get here, validation passed - return the parsed data
        return validationResult.data;
      } catch (error) {
        console.error('Error processing technical implementation:', error);
        return createFallbackTechnicalImplementation(input);
      }
    } catch (error) {
      console.error('Error in technical implementation flow:', error);
      throw error;
    }
  }
);
