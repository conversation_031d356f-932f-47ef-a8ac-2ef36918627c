
'use server';
/**
 * @fileOverview Enhanced web research tool using multiple search APIs for comprehensive research.
 *
 * - performWebResearch - A Genkit tool that performs comprehensive web research using Serper and Brave APIs.
 */

import {ai} from '@/ai/genkit';
import {z} from 'genkit';
import { unifiedSearchEngine } from '@/lib/unified-search-engine';

const PerformWebResearchInputSchema = z.object({
  query: z.string().describe('The search query for web research. Be specific for better results.'),
});
export type PerformWebResearchInput = z.infer<typeof PerformWebResearchInputSchema>;

const PerformWebResearchOutputSchema = z.object({
  findings: z.array(z.string()).describe('A list of key findings, summaries, or relevant information from comprehensive web research across multiple sources.'),
  sources: z.array(z.string()).describe('List of search engines/sources used for the research.'),
  knowledgeGraph: z.string().optional().describe('Knowledge graph information if available.'),
  totalResults: z.number().describe('Total number of unique results found.'),
});
export type PerformWebResearchOutput = z.infer<typeof PerformWebResearchOutputSchema>;

export const performWebResearch = ai.defineTool(
  {
    name: 'performWebResearch',
    description: 'Performs comprehensive web research using multiple search APIs (Serper and Brave) to find current trends, best practices, and information about technologies. Provides high-quality, diverse results from multiple sources.',
    inputSchema: PerformWebResearchInputSchema,
    outputSchema: PerformWebResearchOutputSchema,
  },
  async (input) => {
    const findings: string[] = [];
    let sources: string[] = [];
    let knowledgeGraph: string | undefined;
    let totalResults = 0;

    try {
      // Check if any API keys are available
      const hasSerper = !!process.env.SERPER_API_KEY;
      const hasBrave = !!process.env.BRAVE_API_KEY;

      if (!hasSerper && !hasBrave) {
        console.warn('No search API keys found, falling back to simulated research');
        findings.push(
          `Simulated Finding: For '${input.query}', current best practices emphasize robust design, user-centric features, and leveraging modern technology stacks for optimal performance and maintainability.`
        );
        return {
          findings,
          sources: ['simulated'],
          totalResults: 1,
          knowledgeGraph: undefined
        };
      }

      // Perform comprehensive research using the unified search engine
      console.log(`Starting comprehensive web research for query: "${input.query}"`);
      const researchSummary = await unifiedSearchEngine.search(input.query, {
        maxResults: 8,
        includeKnowledgeGraph: true,
        timeout: 15000,
        parallelExecution: true,
        maxProviders: 3,
        includeBackups: true
      });

      console.log(`Research completed: ${researchSummary.uniqueUrls} unique results from ${researchSummary.sourcesUsed.join(', ')}`);

      // Extract findings from research results
      if (researchSummary.results && researchSummary.results.length > 0) {
        // Process top results into findings
        for (const result of researchSummary.results.slice(0, 5)) {
          if (result.snippet && result.title) {
            const sourceIcon = result.source === 'serper' ? '🔍' :
                              result.source === 'brave' ? '🦁' : '📚';
            const finding = `${sourceIcon} Finding from "${result.title}": ${result.snippet}`;
            findings.push(finding);
          }
        }
      }

      // Set research metadata
      sources = researchSummary.sourcesUsed;
      totalResults = researchSummary.uniqueUrls;

      // Extract knowledge graph if available
      if (researchSummary.knowledgeGraph) {
        knowledgeGraph = `${researchSummary.knowledgeGraph.title}: ${researchSummary.knowledgeGraph.description}`;
        findings.unshift(`📚 Knowledge Graph: ${knowledgeGraph}`);
      }

      // If no results found, provide a fallback
      if (findings.length === 0) {
        findings.push(
          `No specific web results found for '${input.query}'. Consider refining the search query or checking for current trends in this area.`
        );
        totalResults = 0;
      }

    } catch (error) {
      console.error('Web research failed:', error);

      // Check if it's a rate limit error and provide intelligent fallbacks
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      if (errorMessage.includes('Rate limit exceeded') ||
          errorMessage.includes('RATE_LIMIT_EXCEEDED') ||
          errorMessage.includes('429') ||
          errorMessage.includes('Too Many Requests')) {
        // Provide domain-specific fallback knowledge based on query keywords
        const query = input.query.toLowerCase();
        if (query.includes('cloud') || query.includes('aws') || query.includes('azure') || query.includes('gcp')) {
          findings.push(
            `Rate limit reached. Based on current cloud computing trends: Multi-cloud strategies, serverless architectures, and Infrastructure as Code (IaC) are key priorities. Consider AWS Well-Architected Framework, Azure Cloud Adoption Framework, or Google Cloud Architecture Framework for best practices.`
          );
        } else if (query.includes('ai') || query.includes('machine learning') || query.includes('artificial intelligence')) {
          findings.push(
            `Rate limit reached. Current AI trends include: Large Language Models (LLMs), MLOps practices, responsible AI implementation, and edge AI deployment. Focus on data governance, model explainability, and ethical AI considerations.`
          );
        } else if (query.includes('security') || query.includes('cybersecurity') || query.includes('compliance')) {
          findings.push(
            `Rate limit reached. Current security best practices: Zero Trust architecture, DevSecOps integration, continuous compliance monitoring, and privacy-by-design principles. Consider frameworks like NIST, ISO 27001, and SOC 2.`
          );
        } else if (query.includes('data') || query.includes('analytics') || query.includes('database')) {
          findings.push(
            `Rate limit reached. Modern data architecture trends: Data mesh, real-time analytics, cloud-native databases, and data fabric approaches. Focus on data quality, governance, and self-service analytics capabilities.`
          );
        } else {
          findings.push(
            `Rate limit reached for '${input.query}'. Based on current industry knowledge, consider implementing modern best practices such as microservices architecture, containerization, and cloud-native design patterns for scalable solutions.`
          );
        }

        // Add a note about rate limiting and circuit breaker
        findings.push(
          `Note: Some search APIs are temporarily rate limited. The system uses circuit breakers and exponential backoff to handle this gracefully. Serper search is still active and providing results. Brave search will be retried automatically after a cooldown period.`
        );
        sources = ['fallback-rate-limited'];
        totalResults = findings.length;
      } else {
        // Fallback to a generic finding for other errors
        console.error(`Non-rate-limit web research error for "${input.query}":`, errorMessage);
        findings.push(
          `Web research temporarily unavailable for '${input.query}' due to: ${errorMessage.substring(0, 100)}... Recommend consulting current industry best practices and recent publications for up-to-date insights.`
        );
        sources = ['fallback-error'];
        totalResults = findings.length;
      }
    }

    // Limit the number of findings to keep the LLM context manageable
    if (findings.length > 5) {
      findings.splice(5);
    }

    return {
      findings,
      sources: sources.length > 0 ? sources : ['fallback'],
      totalResults,
      knowledgeGraph
    };
  }
);

