// src/services/ai/braveSearchClient.ts

interface IBraveSearchResult {
  title: string;
  url: string;
  snippet: string;
  source?: string; // e.g., "Brave Search"
}

interface IBraveSearchResponse {
  results: IBraveSearchResult[];
}

class BraveSearchClient {
  private apiKey: string;

  constructor(apiKey: string | undefined) {
    if (!apiKey) {
      console.warn('Brave Search API key is not set. Using mock responses.');
      this.apiKey = 'MOCK_API_KEY'; // Use a mock key if undefined
    } else {
      this.apiKey = apiKey;
    }
  }

  /**
   * Simulates performing a web search using the Brave Search API.
   * @param query - The search query string.
   * @returns A Promise resolving to an array of simulated search results.
   */
  public async search(query: string): Promise<IBraveSearchResult[]> {
    console.log(`[BraveSearchClient MOCK] Called search with query: "${query}"`);

    if (this.apiKey === 'MOCK_API_KEY' || !process.env.BRAVE_API_KEY) {
      // Simulate a delay and return a mock response
      await new Promise(resolve => setTimeout(resolve, 300)); // Simulate network latency
      const mockResults: IBraveSearchResult[] = [
        {
          title: `Mock Result 1 for "${query}"`,
          url: `https://example.com/mock-search/${encodeURIComponent(query)}-1`,
          snippet: `This is a mock search result snippet for the query: ${query}. It demonstrates the structure of a Brave search result. (Source: Mock Brave Search)`,
          source: 'Mock Brave Search',
        },
        {
          title: `Mock Result 2 for "${query}"`,
          url: `https://example.com/mock-search/${encodeURIComponent(query)}-2`,
          snippet: `Another detailed mock snippet providing more information related to "${query}". These results are not from a live API. (Source: Mock Brave Search)`,
          source: 'Mock Brave Search',
        },
      ];
      return mockResults;
    }

    // Placeholder for actual API call logic
    // For example:
    // const response = await fetch(`https://api.search.brave.com/res/v1/web/search?q=${encodeURIComponent(query)}`, {
    //   headers: {
    //     'Accept': 'application/json',
    //     'X-Subscription-Token': this.apiKey,
    //   },
    // });
    // if (!response.ok) {
    //   throw new Error(`Brave Search API request failed with status ${response.status}`);
    // }
    // const data = await response.json(); // Actual response structure might differ
    // return data.web.results.map((item: any) => ({ // Adapt mapping to actual API response
    //   title: item.title,
    //   url: item.url,
    //   snippet: item.description,
    //   source: "Brave Search"
    // }));

    // For now, returning a more detailed mock response if API key is present but not used
    return [
      {
        title: `Simulated Brave Search Result 1 for "${query}" (API Key Present)`,
        url: `https://example.com/simulated-search/${encodeURIComponent(query)}-1`,
        snippet: `This is a simulated search result snippet for the query: ${query}, when an API key is detected but the mock is still active.`,
        source: 'Simulated Brave Search',
      }
    ];
  }
}

// Initialize and export a singleton instance of the client
const braveSearchClient = new BraveSearchClient(process.env.BRAVE_API_KEY);

export default braveSearchClient;
