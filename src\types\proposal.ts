// src/types/proposal.ts

export interface ISuggestion {
  id: string;
  text: string;
  type?: 'general' | 'specific' | 'technical'; // Optional: to categorize suggestions
}

export interface IProposal {
  id: string;
  title: string;
  text: string;
  score?: number | null;
  suggestions?: ISuggestion[];
  createdAt: string; // ISO date string
  updatedAt: string; // ISO date string
  // Add any other common fields for a proposal
}

export interface IProposalFeedback {
  score?: number; // e.g., 1-5
  selectedSuggestionTexts?: string[];
  selectedSuggestions?: ISuggestion[]; // Full suggestion objects
}

export interface IAmendRequestData {
  originalProposal: string;
  feedback: IProposalFeedback;
}

export interface IAmendedProposal {
  amendedText: string;
  appliedSuggestions?: ISuggestion[]; // Suggestions that were applied in this amendment
}
