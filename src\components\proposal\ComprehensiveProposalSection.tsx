"use client";

import React from 'react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Zap, Loader2, CheckCircle, Clock } from 'lucide-react';

interface ComprehensiveProposalSectionProps {
  isLoadingComprehensive: boolean;
  comprehensiveProgress: number;
  comprehensiveCurrentStep: string;
  comprehensiveProposal: string;
  technicalImplementation: string;
  showComprehensiveMode: boolean;
  rfpContent: string;
  rfpPdfDataUri: string | null;
  onGenerateComprehensiveProposal: () => void;
  onToggleComprehensiveMode: () => void;
  onComprehensiveProposalChange: (value: string) => void;
  onTechnicalImplementationChange: (value: string) => void;
}

export const ComprehensiveProposalSection: React.FC<ComprehensiveProposalSectionProps> = ({
  isLoadingComprehensive,
  comprehensiveProgress,
  comprehensiveCurrentStep,
  comprehensiveProposal,
  technicalImplementation,
  showComprehensiveMode,
  rfpContent,
  rfpPdfDataUri,
  onGenerateComprehensiveProposal,
  onToggleComprehensiveMode,
  onComprehensiveProposalChange,
  onTechnicalImplementationChange,
}) => {
  const hasRfpInput = rfpContent.trim() || rfpPdfDataUri;

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <Zap className="w-7 h-7 text-primary" />
          <div>
            <h2 className="text-xl font-semibold">🚀 Generate Comprehensive Proposal</h2>
            <p className="text-sm text-muted-foreground">
              Use AI agents to generate a complete technical proposal with Requirements Analysis, Solution Overview, and Technical Implementation sections.
            </p>
          </div>
        </div>
        {isLoadingComprehensive && (
          <Loader2 className="h-5 w-5 animate-spin text-primary" />
        )}
      </div>

      {/* Progress Indicator */}
      {isLoadingComprehensive && (
        <div className="space-y-4">
          <div className="flex items-center justify-between text-sm">
            <span className="text-muted-foreground">{comprehensiveCurrentStep}</span>
            <span className="font-medium">{Math.round(comprehensiveProgress)}%</span>
          </div>
          <div className="w-full bg-secondary rounded-full h-2">
            <div
              className="bg-primary h-2 rounded-full transition-all duration-300 ease-out"
              style={{ width: `${comprehensiveProgress}%` }}
            />
          </div>
          <div className="grid grid-cols-3 gap-2 text-xs">
            <div className={`flex items-center gap-1 ${comprehensiveProgress >= 33 ? 'text-green-600' : 'text-muted-foreground'}`}>
              {comprehensiveProgress >= 33 ? <CheckCircle className="h-3 w-3" /> : <Clock className="h-3 w-3" />}
              Requirements
            </div>
            <div className={`flex items-center gap-1 ${comprehensiveProgress >= 66 ? 'text-green-600' : 'text-muted-foreground'}`}>
              {comprehensiveProgress >= 66 ? <CheckCircle className="h-3 w-3" /> : <Clock className="h-3 w-3" />}
              Solution
            </div>
            <div className={`flex items-center gap-1 ${comprehensiveProgress >= 90 ? 'text-green-600' : 'text-muted-foreground'}`}>
              {comprehensiveProgress >= 90 ? <CheckCircle className="h-3 w-3" /> : <Clock className="h-3 w-3" />}
              Technical
            </div>
          </div>
        </div>
      )}

      {/* Empty State */}
      {!isLoadingComprehensive && !comprehensiveProposal && (
        <div className="text-center py-8 text-muted-foreground">
          <Zap className="h-12 w-12 mx-auto mb-4 opacity-50" />
          <p className="text-lg font-medium mb-2">Ready to Generate Comprehensive Proposal</p>
          <p className="text-sm">
            This will use multiple AI agents to create detailed sections:<br />
            • Requirements Analysis Agent (enhanced)<br />
            • Solution Overview Agent (enhanced)<br />
            • Technical Implementation Agent (new)
          </p>
        </div>
      )}

      {/* Comprehensive Proposal Preview */}
      {comprehensiveProposal && showComprehensiveMode && (
        <div className="mt-4">
          <Label htmlFor="comprehensivePreview">Complete Proposal Preview</Label>
          <Textarea
            id="comprehensivePreview"
            value={comprehensiveProposal}
            onChange={(e) => onComprehensiveProposalChange(e.target.value)}
            placeholder="Comprehensive proposal will appear here..."
            className="min-h-[400px] text-sm bg-white dark:bg-gray-900 font-mono"
            aria-label="Comprehensive Proposal Preview"
          />
        </div>
      )}

      {/* Technical Implementation Preview */}
      {technicalImplementation && !showComprehensiveMode && (
        <div className="mt-4">
          <Label htmlFor="technicalImplementationPreview">Technical Implementation Section</Label>
          <Textarea
            id="technicalImplementationPreview"
            value={technicalImplementation}
            onChange={(e) => onTechnicalImplementationChange(e.target.value)}
            placeholder="Technical implementation details will appear here..."
            className="min-h-[300px] text-sm bg-white dark:bg-gray-900"
            aria-label="Technical Implementation Preview"
          />
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex flex-col sm:flex-row gap-2 w-full">
        <Button
          onClick={onGenerateComprehensiveProposal}
          disabled={isLoadingComprehensive || !hasRfpInput}
          className="flex-1"
        >
          {isLoadingComprehensive ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              {comprehensiveCurrentStep}
            </>
          ) : (
            <>
              <Zap className="mr-2 h-4 w-4" />
              Generate Complete Proposal
            </>
          )}
        </Button>
        {comprehensiveProposal && (
          <Button
            variant="outline"
            onClick={onToggleComprehensiveMode}
            className="flex-shrink-0"
          >
            {showComprehensiveMode ? 'Hide' : 'Show'} Preview
          </Button>
        )}
      </div>
    </div>
  );
};
