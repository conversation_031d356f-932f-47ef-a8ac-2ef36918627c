# Advanced Proposal Generation Module - Design Document

## 1. Introduction

This document outlines the design for the Advanced Proposal Generation Module within the ProposalPilot application. The module aims to automate the creation of detailed and customized proposals by leveraging AI services for content generation (Gemini) and real-time data retrieval (Brave/Serper).

## 2. Data Models

The core data structures for this module are defined as TypeScript interfaces in `src/types/proposal.types.ts`. These include:

-   `IAdvancedProposalRequest`: Defines the input parameters for generating a proposal, such as topic, client needs, desired tone, length, and specific sections with optional research keywords.
-   `IProposalSection`: Represents a single section within a proposal, containing its title, generated content, and order.
-   `IProposalDocument`: Represents the final assembled proposal, including its title, all sections, and metadata.

Refer to `src/types/proposal.types.ts` for detailed definitions.

## 3. API Endpoint

A new API endpoint will be created to handle advanced proposal generation requests.

-   **Endpoint**: `POST /api/generate-advanced-proposal`
-   **Request Body**: `IAdvancedProposalRequest`
-   **Response (Success)**: `IProposalDocument` (HTTP 200 OK)
-   **Response (Error)**: Standard error object (HTTP 400 Bad Request, HTTP 500 Internal Server Error)

### 3.1. Request Validation (Zod)

The request body will be validated using Zod. A schema will be defined in `src/app/api/generate-advanced-proposal/route.ts` (or a shared validation utilities file).

```typescript
// Example Zod Schema (to be placed in the route handler or a validation utility)
import { z } from 'zod';

export const advancedProposalRequestSchema = z.object({
  topic: z.string().min(5, { message: 'Topic must be at least 5 characters long' }),
  clientNeeds: z.string().min(10, { message: 'Client needs description must be at least 10 characters long' }),
  tone: z.enum(['formal', 'informal', 'technical', 'persuasive']),
  length: z.enum(['short', 'medium', 'long']),
  sections: z.array(z.object({
    title: z.string().min(3, { message: 'Section title must be at least 3 characters long' }),
    specificInstructions: z.string().optional(),
    researchKeywords: z.array(z.string()).optional(),
  })).optional(),
  customPrompt: z.string().optional(),
  templateId: z.string().optional(),
});
```

### 3.2. Success Response Example (200 OK)

```json
// IProposalDocument structure
{
  "id": "prop_123xyz",
  "title": "Generated Proposal for Client X",
  "clientId": "client_abc",
  "createdAt": "2025-05-31T12:00:00Z",
  "updatedAt": "2025-05-31T12:05:00Z",
  "sections": [
    {
      "id": "sec_intro",
      "title": "Introduction",
      "content": "This is the generated introduction...",
      "order": 1
    },
    // ... other sections
  ],
  "generationMetadata": {
    "tone": "formal",
    "length": "medium",
    "overallTopic": "AI Solutions for Retail"
  }
}
```

### 3.3. Error Response Example (400 Bad Request)

```json
{
  "error": "Validation Failed",
  "details": [
    { "path": ["topic"], "message": "Topic must be at least 5 characters long" }
  ]
}
```

## 4. Proposal Generation Workflow

The workflow involves several steps:

1.  **Receive Request**: The API endpoint receives an `IAdvancedProposalRequest`.
2.  **Validate Input**: The request payload is validated against the Zod schema.
3.  **Initialize Proposal Document**: Create a preliminary `IProposalDocument` structure.
4.  **Determine Sections**: 
    *   If `request.sections` is provided, use those.
    *   Otherwise, use a default set of sections based on the `topic` or `templateId` (e.g., Introduction, Problem Statement, Proposed Solution, Timeline, Pricing, Conclusion).
5.  **Iterate and Generate Sections**:
    For each section to be generated:
    a.  **Construct Base Prompt**: Create a base prompt for Gemini based on `request.topic`, `request.clientNeeds`, `request.tone`, `request.length`, and `section.title`, `section.specificInstructions`.
    b.  **Perform Research (if applicable)**: 
        *   If `section.researchKeywords` are provided and non-empty, call Brave Search API and/or Serper API to gather relevant real-time information, articles, or data points.
        *   Process and summarize the research results to extract key insights.
    c.  **Augment Prompt**: Integrate the summarized research insights into the Gemini prompt to provide context and factual grounding.
    d.  **Call Gemini Service**: Send the final prompt to the Gemini service (`src/services/ai/geminiService.ts`).
    e.  **Store Section Content**: Add the generated content to the corresponding `IProposalSection` object.
6.  **Assemble Document**: Compile all generated `IProposalSection` objects into the `IProposalDocument.sections` array, ensuring correct order.
7.  **Finalize Metadata**: Populate `generationMetadata` and `updatedAt` fields.
8.  **Return Document**: Send the complete `IProposalDocument` as the API response.

**(Optional) Persistence**: While not explicitly in this task's scope for initial design, future iterations might involve saving the `IProposalDocument` to a database (e.g., MongoDB, PostgreSQL) and returning a reference or the saved document.

## 5. Service Interactions

-   **Gemini Service (`src/services/ai/geminiService.ts`)**:
    *   **Input**: A well-structured text prompt, potentially including context from research.
    *   **Action**: Generates human-like text content for a proposal section.
    *   **Output**: String containing the generated content.
    *   The existing `geminiService.ts` will be used or extended if necessary to support more complex prompting strategies.

-   **Brave Search API / Serper API**:
    *   **Input**: A list of keywords (`section.researchKeywords`).
    *   **Action**: Fetches search results (links, snippets, articles).
    *   **Output**: Raw search results (e.g., JSON array of search hits).
    *   A new service or utility (e.g., `src/services/ai/researchService.ts`) will be created to abstract these calls and process their outputs.
    *   This service will handle fetching content from top N results and summarizing it.

## 6. Error Handling

Robust error handling is crucial:

-   **API Request Validation**: Zod errors will be caught, and a 400 Bad Request response with detailed error messages will be returned.
-   **AI Service Errors**:
    *   **Gemini**: Handle API errors (e.g., rate limits, invalid requests, content filtering), timeouts. Implement retries with exponential backoff for transient errors. If generation fails for a section, either return an error, skip the section with a warning, or use placeholder content.
    *   **Brave/Serper**: Handle API errors, timeouts, or no results found. If research fails, proceed with generation without research augmentation for that section, possibly logging a warning.
-   **Network Issues**: General network connectivity problems when calling external services.
-   **Internal Server Errors**: Catch-all for unexpected errors, returning a 500 Internal Server Error. Log detailed error information for debugging.
-   **Partial Success**: Consider strategies for partial success (e.g., if some sections generate but others fail). The initial approach will be to fail the entire request if a critical section cannot be generated.

## 7. Integration Points

-   **Frontend**: UI components (e.g., a new form in the Next.js application) will be developed to collect user input for `IAdvancedProposalRequest` and display the generated `IProposalDocument`.
-   **Backend (API Route)**: The `POST /api/generate-advanced-proposal` route handler in `src/app/api/generate-advanced-proposal/route.ts` will orchestrate the workflow.
-   **AI Services**: Integration with `geminiService.ts` and the new `researchService.ts`.
-   **Database (Future)**: If proposals are persisted, integration with the chosen database schema and ORM/client will be required.
-   **Logging**: Implement comprehensive logging throughout the workflow using a standard logger (e.g., Pino) to track request flow, AI service calls, errors, and performance metrics.

## 8. Future Considerations

-   **Advanced Templating**: Allow users to create and manage proposal templates.
-   **Real-time Collaboration**: Features for multiple users to work on a proposal.
-   **Version History**: Track changes and revisions to proposals.
-   **Feedback Loop**: Allow users to rate generated content to fine-tune AI models or prompts.
-   **Streaming Responses**: For long proposals, consider streaming section content as it's generated to improve perceived performance.
-   **Caching**: Cache research results or even section generations for common requests to reduce latency and API costs.
