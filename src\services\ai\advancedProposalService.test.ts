// src/services/ai/advancedProposalService.test.ts
import { AdvancedProposalService } from './advancedProposalService';
import { IAdvancedProposalRequest, IProposalDocument } from '@/types/proposal.types';
import geminiClient from './geminiClient';
import braveSearchClient from './braveSearchClient';

// Mock the AI clients
jest.mock('./geminiClient', () => ({
  generateText: jest.fn(),
}));

jest.mock('./braveSearchClient', () => ({
  search: jest.fn(),
}));

describe('AdvancedProposalService', () => {
  const mockTopic = 'Test Topic';
  const mockClientNeeds = 'Test Client Needs';
  const mockTone = 'formal';
  const mockLength = 'medium';

  const baseRequest: IAdvancedProposalRequest = {
    topic: mockTopic,
    clientNeeds: mockClientNeeds,
    tone: mockTone,
    length: mockLength,
  };

  beforeEach(() => {
    // Reset mocks before each test
    (geminiClient.generateText as jest.Mock).mockReset();
    (braveSearchClient.search as jest.Mock).mockReset();
  });

  it('should generate a proposal with default sections if none are provided', async () => {
    (geminiClient.generateText as jest.Mock).mockImplementation(async ({ prompt }: { prompt: string }) => {
      if (prompt.includes('Introduction')) return 'Mocked Introduction Content';
      if (prompt.includes('Understanding Client Needs')) return 'Mocked Client Needs Content';
      if (prompt.includes('Proposed Solution')) return 'Mocked Solution Content';
      if (prompt.includes('Conclusion')) return 'Mocked Conclusion Content';
      return 'Default Mocked Content';
    });

    const proposal = await AdvancedProposalService.generate(baseRequest);

    expect(proposal).toBeDefined();
    expect(proposal.title).toBe(`Proposal: ${mockTopic}`);
    expect(proposal.sections).toHaveLength(4); // Default sections
    expect(proposal.sections[0].title).toBe('Introduction');
    expect(proposal.sections[0].content).toBe('Mocked Introduction Content');
    expect(geminiClient.generateText).toHaveBeenCalledTimes(4);
    expect(braveSearchClient.search).not.toHaveBeenCalled();
  });

  it('should generate a proposal with user-defined sections', async () => {
    (geminiClient.generateText as jest.Mock).mockResolvedValue('Mocked Custom Section Content');
    
    const customSectionsRequest: IAdvancedProposalRequest = {
      ...baseRequest,
      sections: [{ title: 'Custom Section 1' }, { title: 'Custom Section 2' }],
    };

    const proposal = await AdvancedProposalService.generate(customSectionsRequest);

    expect(proposal).toBeDefined();
    expect(proposal.sections).toHaveLength(2);
    expect(proposal.sections[0].title).toBe('Custom Section 1');
    expect(proposal.sections[0].content).toBe('Mocked Custom Section Content');
    expect(geminiClient.generateText).toHaveBeenCalledTimes(2);
  });

  it('should call braveSearchClient if researchKeywords are provided', async () => {
    (geminiClient.generateText as jest.Mock).mockResolvedValue('Mocked Content with Research');
    (braveSearchClient.search as jest.Mock).mockResolvedValue([
      { title: 'Research Result 1', snippet: 'Snippet 1', url: 'url1.com' },
    ]);

    const requestWithKeywords: IAdvancedProposalRequest = {
      ...baseRequest,
      sections: [{ title: 'Research Section', researchKeywords: ['keyword1', 'keyword2'] }],
    };

    const proposal = await AdvancedProposalService.generate(requestWithKeywords);

    expect(braveSearchClient.search).toHaveBeenCalledWith('keyword1 keyword2');
    expect(proposal.sections[0].content).toContain('Research Result 1');
    expect(proposal.sections[0].content).toContain('url1.com');
  });

  it('should throw an error for invalid request (missing topic)', async () => {
    const invalidRequest: any = { ...baseRequest, topic: '' }; // topic is required
    await expect(AdvancedProposalService.generate(invalidRequest)).rejects.toThrowError(/Invalid request: topic: Topic is required/);
  });
  
  it('should throw an error for invalid request (invalid tone)', async () => {
    const invalidRequest: any = { ...baseRequest, tone: 'casual' }; // 'casual' is not a valid tone
    await expect(AdvancedProposalService.generate(invalidRequest)).rejects.toThrowError(/Invalid request: tone: Invalid enum value./);
  });

  it('should correctly use customPrompt if provided', async () => {
    const customPromptText = "This is a very specific custom prompt.";
    (geminiClient.generateText as jest.Mock).mockImplementation(async ({ prompt }: { prompt: string }) => {
        expect(prompt).toContain(customPromptText);
        return `Content generated with custom prompt: ${prompt.substring(0,50)}`;
    });
    
    const requestWithCustomPrompt: IAdvancedProposalRequest = {
      ...baseRequest,
      customPrompt: customPromptText,
      sections: [{ title: 'Section With Custom Prompt' }],
    };

    await AdvancedProposalService.generate(requestWithCustomPrompt);
    expect(geminiClient.generateText).toHaveBeenCalled();
    // The check for customPromptText is inside the mockImplementation
  });

  // TODO: Add test for templateId handling (once implemented)
  // TODO: Add tests for error handling from AI clients (e.g., if geminiClient.generateText throws an error)
});
