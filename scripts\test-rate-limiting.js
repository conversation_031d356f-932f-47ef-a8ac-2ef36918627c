#!/usr/bin/env node

/**
 * Test script to verify rate limiting and fallback behavior
 */

require('dotenv').config({ path: '.env.local' });

async function testRateLimiting() {
  console.log('🔄 Testing Rate Limiting and Fallback System...\n');

  const queries = [
    'cloud computing best practices',
    'artificial intelligence trends 2024',
    'cybersecurity frameworks',
    'data analytics modern approaches',
    'microservices architecture patterns'
  ];

  for (let i = 0; i < queries.length; i++) {
    const query = queries[i];
    console.log(`\n${i + 1}. Testing query: "${query}"`);
    
    try {
      const startTime = Date.now();
      
      const response = await fetch('http://localhost:9002/api/test-brave-search', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ query }),
      });

      const endTime = Date.now();
      const data = await response.json();

      console.log(`   ⏱️  Response time: ${endTime - startTime}ms`);
      
      if (data.success && data.findings) {
        const finding = data.findings[0];
        if (finding.includes('Rate limit reached')) {
          console.log('   🚦 Rate limit fallback triggered');
          console.log(`   💡 Fallback: ${finding.substring(0, 100)}...`);
        } else if (finding.includes('Web Finding')) {
          console.log('   ✅ Real web search successful');
          console.log(`   📄 Result: ${finding.substring(0, 100)}...`);
        } else {
          console.log('   ⚠️  Other response type');
          console.log(`   📄 Response: ${finding.substring(0, 100)}...`);
        }
      } else {
        console.log('   ❌ Request failed:', data.error || 'Unknown error');
      }

      // Small delay between requests to see rate limiting in action
      if (i < queries.length - 1) {
        console.log('   ⏳ Waiting 500ms before next request...');
        await new Promise(resolve => setTimeout(resolve, 500));
      }

    } catch (error) {
      console.log(`   ❌ Request failed: ${error.message}`);
    }
  }

  console.log('\n🏁 Rate limiting test completed!');
  console.log('\n📊 Summary:');
  console.log('- If you see "Real web search successful" - API is working normally');
  console.log('- If you see "Rate limit fallback triggered" - Fallback system is working');
  console.log('- The system should gracefully handle both scenarios');
}

// Run the test
testRateLimiting().catch(console.error);
