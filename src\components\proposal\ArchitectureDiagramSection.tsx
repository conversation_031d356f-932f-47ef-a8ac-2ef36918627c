"use client";

import React, { lazy, Suspense } from 'react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { SectionCard } from './SectionCard';
import { Separator } from '@/components/ui/separator';
import { Network, Sparkles, Loader2 } from 'lucide-react';

// Lazy load MermaidPreview for better performance
const MermaidPreview = lazy(() => import('@/components/MermaidPreview').then(module => ({ default: module.MermaidPreview })));

interface ArchitectureDiagramSectionProps {
  architectureInput: string;
  diagramType: 'conceptual' | 'reference';
  mermaidScript: string;
  mermaidSvg: string;
  isLoadingDiagram: boolean;
  onArchitectureInputChange: (value: string) => void;
  onDiagramTypeChange: (value: 'conceptual' | 'reference') => void;
  onMermaidScriptChange: (value: string) => void;
  onMermaidSvgChange: (value: string) => void;
  onGenerateDiagram: () => void;
}

export const ArchitectureDiagramSection: React.FC<ArchitectureDiagramSectionProps> = ({
  architectureInput,
  diagramType,
  mermaidScript,
  mermaidSvg,
  isLoadingDiagram,
  onArchitectureInputChange,
  onDiagramTypeChange,
  onMermaidScriptChange,
  onMermaidSvgChange,
  onGenerateDiagram,
}) => {
  return (
    <SectionCard
      title="5. Architecture Diagram"
      description="Generate technical architecture diagrams using AI and Mermaid syntax."
      icon={Network}
    >
      <div className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label htmlFor="architectureInput">Architecture Description</Label>
            <Textarea
              id="architectureInput"
              value={architectureInput}
              onChange={(e) => onArchitectureInputChange(e.target.value)}
              placeholder="Describe the architecture you want to visualize..."
              className="min-h-[120px] text-sm"
              aria-label="Architecture Description Input"
            />
          </div>
          
          <div className="space-y-4">
            <div>
              <Label htmlFor="diagramType">Diagram Type</Label>
              <Select value={diagramType} onValueChange={onDiagramTypeChange}>
                <SelectTrigger>
                  <SelectValue placeholder="Select diagram type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="conceptual">Conceptual Architecture</SelectItem>
                  <SelectItem value="reference">Reference Architecture</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <Button
              onClick={onGenerateDiagram}
              disabled={isLoadingDiagram || !architectureInput.trim()}
              className="w-full"
            >
              {isLoadingDiagram ? (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              ) : (
                <Sparkles className="mr-2 h-4 w-4" />
              )}
              Generate Diagram with AI
            </Button>
          </div>
        </div>

        <Separator className="my-6" />

        <Suspense fallback={
          <div className="flex items-center justify-center p-8">
            <Loader2 className="h-6 w-6 animate-spin" />
            <span className="ml-2">Loading diagram editor...</span>
          </div>
        }>
          <MermaidPreview
            initialScript={mermaidScript}
            onScriptChange={onMermaidScriptChange}
            onSvgChange={onMermaidSvgChange}
            title="Diagram Editor & Preview"
            description="Edit the Mermaid script directly or refine the AI-generated version."
          />
        </Suspense>
      </div>
    </SectionCard>
  );
};
