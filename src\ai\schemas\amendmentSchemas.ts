// src/ai/schemas/amendmentSchemas.ts
import { z } from 'zod';
import type { IProposalFeedback, IAmendRequestData, IAmendedProposal, ISuggestion } from '@/types/proposal';

// Schema for ISuggestion
const suggestionSchema: z.ZodType<ISuggestion> = z.object({
  id: z.string(),
  text: z.string(),
  type: z.enum(['general', 'specific', 'technical']).optional(),
});

// Schema for IProposalFeedback
const proposalFeedbackSchema: z.ZodType<IProposalFeedback> = z.object({
  score: z.number().int().min(1).max(5).optional(), // Assuming score is 1-5, integer
  selectedSuggestionTexts: z.array(z.string()).optional(),
  selectedSuggestions: z.array(suggestionSchema).optional(), // Added for full suggestion objects
});

// Schema for IAmendRequestData
export const amendRequestSchema: z.ZodType<IAmendRequestData> = z.object({
  originalProposal: z.string().min(1, { message: "Original proposal cannot be empty." }),
  feedback: proposalFeedbackSchema,
});

// Schema for IAmendedProposal
export const amendedProposalSchema: z.ZodType<IAmendedProposal> = z.object({
  amendedText: z.string(),
  appliedSuggestions: z.array(suggestionSchema).optional(), // Added for applied suggestions
});
