// src/services/ai/advancedProposalService.ts
import { v4 as uuidv4 } from 'uuid';
import { IAdvancedProposalRequest, AdvancedProposalRequestSchema, IProposalDocument, IProposalSection } from '@/types/proposal.types';
// AI service clients
import geminiClient from './geminiClient';
import braveSearchClient from './braveSearchClient';
// import serperClient from '@/services/ai/serperClient'; // Serper client placeholder, not yet implemented

export class AdvancedProposalService {
  /**
   * Generates an advanced proposal based on the user's request.
   * Orchestrates calls to various AI services for content generation and research.
   *
   * @param request - The advanced proposal request data.
   * @returns A Promise resolving to the generated IProposalDocument.
   * @throws Error if validation fails or AI services encounter critical errors.
   */
  public static async generate(request: IAdvancedProposalRequest): Promise<IProposalDocument> {
    // Validate the request using Zod schema
    // Note: Validation might also be done at the API route level before calling the service.
    const validationResult = AdvancedProposalRequestSchema.safeParse(request);
    if (!validationResult.success) {
      // Combine Zod error messages into a single string or return a structured error
      const errorMessage = validationResult.error.errors.map(e => `${e.path.join('.')}: ${e.message}`).join(', ');
      throw new Error(`Invalid request: ${errorMessage}`);
    }

    const { topic, clientNeeds, tone, length, sections: requestedSections, customPrompt, templateId } = validationResult.data;

    console.log(`Starting advanced proposal generation for topic: ${topic}`);

    // TODO: Implement template fetching if templateId is provided

    // Define default sections if not provided or enhance them
    const sectionsToGenerate = requestedSections && requestedSections.length > 0
      ? requestedSections
      : [
          { title: 'Introduction', specificInstructions: `Introduce the proposal topic: ${topic} focusing on ${clientNeeds}.` },
          { title: 'Understanding Client Needs', specificInstructions: `Detail the understanding of ${clientNeeds}.` },
          { title: 'Proposed Solution', specificInstructions: `Outline the proposed solution for ${clientNeeds} related to ${topic}.` },
          { title: 'Conclusion', specificInstructions: `Conclude the proposal for ${topic}.` },
        ];

    const generatedSections: IProposalSection[] = [];
    let sectionOrder = 0;

    // Asynchronously generate content for each section
    // This is a simplified example; real implementation might involve more complex orchestration
    for (const sectionSpec of sectionsToGenerate) {
      sectionOrder++;
      let sectionContent = '';
      
      // AI-driven content generation using Gemini with error handling
      try {
        const geminiPrompt = `Generate content for a proposal section titled "${sectionSpec.title}".
                 Topic: ${topic}. Client Needs: ${clientNeeds}. Tone: ${tone}. Length: ${length}.
                 Specific Instructions: ${sectionSpec.specificInstructions || 'N/A'}.
                 ${customPrompt ? `Overall Custom Prompt: ${customPrompt}` : ''}`;
        
        sectionContent = await geminiClient.generateText({ prompt: geminiPrompt });
      } catch (error) {
        console.error(`Error generating content for section "${sectionSpec.title}":`, error);
        sectionContent = `[AI Service Temporarily Unavailable] Content for ${sectionSpec.title} could not be generated at this time.`;
      }

      // Research augmentation using Brave Search with error handling
      const researchKeywords = sectionSpec.researchKeywords ?? [];
      if (researchKeywords.length > 0) {
        try {
          const searchResults = await braveSearchClient.search(researchKeywords.join(' '));
          if (searchResults?.length > 0) {
            const researchSummary = searchResults
              .map(r => `- ${r.title}: ${r.snippet} (Source: ${r.url})`)
              .join('\n');
            sectionContent += `\n\nSupporting Research:\n${researchSummary}`;
          }
        } catch (error) {
          console.error('Error fetching research data:', error);
          sectionContent += '\n\n[Note: Research data could not be loaded at this time]';
        }
      }
      
      generatedSections.push({
        id: `section-${uuidv4()}`, // Unique ID using UUID v4
        title: sectionSpec.title,
        content: sectionContent,
        order: sectionOrder,
        sourcePrompt: sectionSpec.specificInstructions,
        sourceKeywords: sectionSpec.researchKeywords,
      });
    }

    // Construct the final proposal document
    const proposalDocument: IProposalDocument = {
      id: `proposal-${uuidv4()}`, // Unique ID using UUID v4
      title: `Proposal: ${topic}`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      sections: generatedSections,
      generationMetadata: {
        tone,
        length,
        overallTopic: topic,
      },
    };

    console.log(`Advanced proposal generated successfully for topic: ${topic}`);
    return proposalDocument;
  }
}

// Example usage (for testing purposes, would not be in the final service file like this)
/*
async function testService() {
  try {
    const request: IAdvancedProposalRequest = {
      topic: "New E-commerce Platform",
      clientNeeds: "A scalable and modern online store with AI-powered recommendations.",
      tone: "persuasive",
      length: "medium",
      sections: [
        { title: "Executive Summary", specificInstructions: "Highlight key benefits and ROI." },
        { title: "Platform Features", researchKeywords: ["e-commerce trends", "AI recommendation engines"] },
        { title: "Project Timeline" },
      ]
    };
    const document = await AdvancedProposalService.generate(request);
    console.log('Generated Document:', JSON.stringify(document, null, 2));
  } catch (error) {
    console.error('Service Test Error:', error.message);
  }
}
// testService();
*/
