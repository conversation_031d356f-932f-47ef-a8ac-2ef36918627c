'use server';

import { logServerError, isExpectedError } from '@/lib/error-utils';
import { UserPreferencesSchema } from '../schemas/user-preferences';

/**
 * @fileOverview Orchestrates the sequential agent chain for comprehensive proposal generation.
 * Coordinates Requirements Analysis → Solution Overview → Technical Implementation agents.
 *
 * - generateComprehensiveProposal - Main orchestrator function
 * - ProposalOrchestratorInput - Input type for the orchestrator
 * - ProposalOrchestratorOutput - Output type with all sections
 */

import {z} from 'genkit';
import type { AnalyzeRequirementsInput, AnalyzeRequirementsOutput } from './requirements-analyzer';
import { analyzeRequirements } from './requirements-analyzer';
import type { SolutionOverviewInput, SolutionOverviewOutput } from './solution-synthesizer';
import { generateSolutionOverview } from './solution-synthesizer';
import type { TechnicalImplementationInput, TechnicalImplementationOutput } from './technical-implementation';
import { generateTechnicalImplementation } from './technical-implementation';

// Shared context interface for inter-agent communication
export interface ProposalContext {
  requirementsAnalysis?: AnalyzeRequirementsOutput;
  solutionOverview?: SolutionOverviewOutput;
  technicalImplementation?: TechnicalImplementationOutput;
  currentStep: 'requirements' | 'solution' | 'technical' | 'complete';
  progress: number; // 0-100
}

const ProposalOrchestratorInputSchema = z.object({
  // Initial RFP input (same as AnalyzeRequirementsInput)
  inputType: z.enum(['text', 'pdf']).describe('Type of RFP input'),
  rfpContent: z.string().optional().describe('RFP content as text (required if inputType is text)'),
  rfpPdfDataUri: z.string().optional().describe('RFP PDF as data URI (required if inputType is pdf)'),

  // Optional: specific sections to generate
  generateRequirements: z.boolean().default(true).describe('Whether to generate requirements analysis'),
  generateSolution: z.boolean().default(true).describe('Whether to generate solution overview'),
  generateTechnical: z.boolean().default(true).describe('Whether to generate technical implementation'),

  // Optional: user preferences and considerations
  userPreferences: UserPreferencesSchema.optional().describe('User preferences and considerations to guide proposal generation'),

  // Optional: existing context for partial generation
  existingContext: z.object({
    requirementsAnalysis: z.any().optional(),
    solutionOverview: z.any().optional(),
    technicalImplementation: z.any().optional(),
  }).optional().describe('Existing context for partial generation'),
});
export type ProposalOrchestratorInput = z.infer<typeof ProposalOrchestratorInputSchema>;

const ProposalOrchestratorOutputSchema = z.object({
  // Complete proposal sections
  requirementsAnalysis: z.object({
    understandingOfTheRequirements: z.string(),
    functionalRequirements: z.array(z.string()),
    nonFunctionalRequirements: z.array(z.string()),
    technicalConstraints: z.array(z.string()),
    integrationRequirements: z.array(z.string()),
    complianceRequirements: z.array(z.string()),
  }).optional(),

  solutionOverview: z.object({
    solutionOverview: z.string(),
    suggestedOemSolutions: z.array(z.string()),
    proposedArchitecture: z.string(),
    technologyStack: z.array(z.string()),
    keyFeatures: z.array(z.string()),
    securityConsiderations: z.array(z.string()),
    scalabilityApproach: z.string(),
    integrationStrategy: z.string(),
  }).optional(),

  technicalImplementation: z.object({
    technicalImplementation: z.string(),
    developmentMethodology: z.string(),
    implementationPhases: z.array(z.object({
      phase: z.string(),
      duration: z.string(),
      deliverables: z.array(z.string()),
      milestones: z.array(z.string())
    })),
    riskMitigation: z.array(z.object({
      risk: z.string(),
      impact: z.enum(['Low', 'Medium', 'High']),
      mitigation: z.string()
    })),
    qualityAssurance: z.array(z.string()),
    performanceOptimization: z.array(z.string()),
    maintenanceApproach: z.string(),
  }).optional(),

  // Metadata
  generationMetadata: z.object({
    totalSections: z.number(),
    generatedSections: z.number(),
    estimatedWordCount: z.number(),
    generationTime: z.number(), // in seconds
    agentsUsed: z.array(z.string()),
  }),

  // Combined markdown output
  completeProposal: z.string().describe('Complete proposal in markdown format combining all sections'),
});
export type ProposalOrchestratorOutput = z.infer<typeof ProposalOrchestratorOutputSchema>;

export async function generateComprehensiveProposal(
  input: ProposalOrchestratorInput
): Promise<ProposalOrchestratorOutput> {
  const startTime = Date.now();
  const agentsUsed: string[] = ['Proposal Orchestrator'];
  let estimatedWordCount = 0;
  let fallbackMode = false;

  // Validate input
  if (input.inputType === 'text' && !input.rfpContent?.trim()) {
    throw new Error('RFP content is required when inputType is text');
  }
  if (input.inputType === 'pdf' && !input.rfpPdfDataUri) {
    throw new Error('RFP PDF data URI is required when inputType is pdf');
  }

  let requirementsResult: AnalyzeRequirementsOutput | undefined;
  let solutionResult: SolutionOverviewOutput | undefined;
  let technicalResult: TechnicalImplementationOutput | undefined;

  // Early validation for required requirements analysis
  if (!input.generateRequirements && !input.existingContext?.requirementsAnalysis) {
    throw new Error('Existing requirements analysis is required when generateRequirements is disabled');
  }

  try {
    // Step 1: Requirements Analysis
    if (input.generateRequirements && !input.existingContext?.requirementsAnalysis) {

      const requirementsInput: AnalyzeRequirementsInput = input.inputType === 'text'
        ? {
            inputType: 'text',
            rfpContent: input.rfpContent!,
            userPreferences: input.userPreferences
          }
        : {
            inputType: 'pdf',
            rfpPdfDataUri: input.rfpPdfDataUri!,
            userPreferences: input.userPreferences
          };

      requirementsResult = await analyzeRequirements(requirementsInput);
      agentsUsed.push('Requirements Analysis Agent');
      estimatedWordCount += estimateWordCount(requirementsResult.understandingOfTheRequirements);
    } else if (input.existingContext?.requirementsAnalysis) {
      requirementsResult = input.existingContext.requirementsAnalysis as AnalyzeRequirementsOutput;
    }

    // Step 2: Solution Overview
    if (input.generateSolution && !input.existingContext?.solutionOverview) {
      if (!requirementsResult) {
        throw new Error('Requirements analysis is required for solution overview generation');
      }

      const solutionInput: SolutionOverviewInput & { _fallbackMode?: boolean } = {
        rfpRequirements: input.rfpContent || 'RFP requirements from PDF analysis',
        functionalRequirements: requirementsResult.functionalRequirements,
        nonFunctionalRequirements: requirementsResult.nonFunctionalRequirements,
        userPreferences: {
          ...input.userPreferences,
          technicalConstraints: requirementsResult.technicalConstraints,
          complianceRequirements: requirementsResult.complianceRequirements,
          integrationRequirements: requirementsResult.integrationRequirements,
        },
        _fallbackMode: fallbackMode
      };

      try {
        solutionResult = await generateSolutionOverview(solutionInput);
        agentsUsed.push('Solution Overview Agent');
        estimatedWordCount += estimateWordCount(solutionResult.solutionOverview);
      } catch (error) {
        const errorContext = 'Solution overview generation failed';
        logServerError(error, errorContext);
        
        // If we're not already in fallback mode and the error is not expected, try again with fallback
        if (!fallbackMode && !isExpectedError(error)) {
          logServerError('Retrying solution overview with fallback mode', 'Solution Generation');
          solutionInput._fallbackMode = true;
          solutionResult = await generateSolutionOverview(solutionInput);
          agentsUsed.push('Solution Overview Agent (Fallback Mode)');
          estimatedWordCount += estimateWordCount(solutionResult.solutionOverview);
          fallbackMode = true;
        } else {
          throw error; // Re-throw if we're already in fallback mode or it's an expected error
        }
      }
    } else if (input.existingContext?.solutionOverview) {
      solutionResult = input.existingContext.solutionOverview as SolutionOverviewOutput;
    }

    // Step 3: Technical Implementation
    if (input.generateTechnical && !input.existingContext?.technicalImplementation) {
      if (!requirementsResult || !solutionResult) {
        throw new Error('Requirements analysis and solution overview are required for technical implementation');
      }

      const technicalInput: TechnicalImplementationInput = {
        proposedArchitecture: solutionResult.proposedArchitecture,
        technologyStack: solutionResult.technologyStack,
        keyFeatures: solutionResult.keyFeatures,
        securityConsiderations: solutionResult.securityConsiderations,
        scalabilityApproach: solutionResult.scalabilityApproach,
        integrationStrategy: solutionResult.integrationStrategy,
        functionalRequirements: requirementsResult.functionalRequirements,
        nonFunctionalRequirements: requirementsResult.nonFunctionalRequirements,
        technicalConstraints: requirementsResult.technicalConstraints,
        integrationRequirements: requirementsResult.integrationRequirements,
        complianceRequirements: requirementsResult.complianceRequirements,
        userPreferences: input.userPreferences,
      };

      technicalResult = await generateTechnicalImplementation(technicalInput);
      agentsUsed.push('Technical Implementation Agent');
      estimatedWordCount += estimateWordCount(technicalResult.technicalImplementation);
    } else if (input.existingContext?.technicalImplementation) {
      technicalResult = input.existingContext.technicalImplementation as TechnicalImplementationOutput;
    }

    // Step 4: Combine into complete proposal
    const completeProposal = generateCompleteProposalMarkdown(
      requirementsResult,
      solutionResult,
      technicalResult
    );

    const endTime = Date.now();
    const generationTime = Math.round((endTime - startTime) / 1000);

    return {
      requirementsAnalysis: requirementsResult,
      solutionOverview: solutionResult,
      technicalImplementation: technicalResult,
      generationMetadata: {
        totalSections: [input.generateRequirements, input.generateSolution, input.generateTechnical].filter(Boolean).length,
        generatedSections: agentsUsed.length,
        estimatedWordCount,
        generationTime,
        agentsUsed,
      },
      completeProposal,
    };

  } catch (error) {
    console.error('Error in proposal orchestration:', error);
    throw new Error(`Proposal generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

// Helper function to estimate word count
function estimateWordCount(text: string): number {
  return text.split(/\s+/).filter(word => word.length > 0).length;
}

// Helper function to generate complete proposal markdown
function generateCompleteProposalMarkdown(
  requirements?: AnalyzeRequirementsOutput,
  solution?: SolutionOverviewOutput,
  technical?: TechnicalImplementationOutput
): string {
  let markdown = '# Comprehensive Technical Proposal\n\n';

  if (requirements) {
    markdown += '## Understanding the Requirements\n\n';
    markdown += requirements.understandingOfTheRequirements + '\n\n';
  }

  if (solution) {
    markdown += '## Solution Overview\n\n';
    markdown += solution.solutionOverview + '\n\n';

    if (solution.suggestedOemSolutions.length > 0) {
      markdown += '### Suggested OEM Solutions\n\n';
      solution.suggestedOemSolutions.forEach(oem => {
        markdown += `- ${oem}\n`;
      });
      markdown += '\n';
    }
  }

  if (technical) {
    markdown += '## Technical Implementation\n\n';
    markdown += technical.technicalImplementation + '\n\n';

    if (technical.implementationPhases.length > 0) {
      markdown += '### Implementation Timeline\n\n';
      technical.implementationPhases.forEach((phase, index) => {
        markdown += `#### ${phase.phase}\n`;
        markdown += `**Duration:** ${phase.duration}\n\n`;

        if (phase.deliverables.length > 0) {
          markdown += '**Deliverables:**\n';
          phase.deliverables.forEach(deliverable => {
            markdown += `- ${deliverable}\n`;
          });
          markdown += '\n';
        }

        if (phase.milestones.length > 0) {
          markdown += '**Milestones:**\n';
          phase.milestones.forEach(milestone => {
            markdown += `- ${milestone}\n`;
          });
          markdown += '\n';
        }
      });
    }

    if (technical.riskMitigation.length > 0) {
      markdown += '### Risk Management\n\n';
      markdown += '| Risk | Impact | Mitigation Strategy |\n';
      markdown += '|------|--------|--------------------|\n';
      technical.riskMitigation.forEach(risk => {
        markdown += `| ${risk.risk} | ${risk.impact} | ${risk.mitigation} |\n`;
      });
      markdown += '\n';
    }
  }

  return markdown;
}
