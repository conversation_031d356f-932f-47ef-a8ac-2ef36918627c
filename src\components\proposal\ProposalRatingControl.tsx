import React from 'react';
import { Slider } from '@/components/ui/slider';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface IProposalRatingControlProps {
  currentScore: number | null;
  onScoreChange: (score: number) => void;
  maxScore?: number;
  minScore?: number;
  step?: number;
}

const ProposalRatingControl: React.FC<IProposalRatingControlProps> = ({
  currentScore,
  onScoreChange,
  maxScore = 5,
  minScore = 1,
  step = 1,
}) => {
  const handleSliderChange = (value: number[]) => {
    onScoreChange(value[0]);
  };

  return (
    <Card className="w-full md:w-1/2 lg:w-1/3">
      <CardHeader>
        <CardTitle>Rate Proposal</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center justify-between">
          <Label htmlFor="proposal-rating" className="text-lg">
            Your Score: {currentScore !== null ? currentScore : 'Not rated'}
          </Label>
        </div>
        <Slider
          id="proposal-rating"
          min={minScore}
          max={maxScore}
          step={step}
          value={currentScore !== null ? [currentScore] : [Math.floor((minScore + maxScore) / 2)]}
          onValueChange={handleSliderChange}
          className="w-full"
        />
        <div className="flex justify-between text-xs text-muted-foreground">
          <span>{minScore}</span>
          <span>{maxScore}</span>
        </div>
      </CardContent>
    </Card>
  );
};

export default ProposalRatingControl;
