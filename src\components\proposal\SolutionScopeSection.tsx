"use client";

import React from 'react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { SectionCard } from './SectionCard';
import { KeyRound, Combine, Loader2 } from 'lucide-react';

interface SolutionScopeSectionProps {
  solutionKeyPoints: string;
  solutionOverview: string;
  oemSolutions: string[];
  solutionOverviewAttempted: boolean;
  isLoadingSolution: boolean;
  onSolutionKeyPointsChange: (value: string) => void;
  onSolutionOverviewChange: (value: string) => void;
  onSynthesizeSolution: () => void;
}

export const SolutionScopeSection: React.FC<SolutionScopeSectionProps> = ({
  solutionKeyPoints,
  solutionOverview,
  oemSolutions,
  solutionOverviewAttempted,
  isLoadingSolution,
  onSolutionKeyPointsChange,
  onSolutionOverviewChange,
  onSynthesizeSolution,
}) => {
  return (
    <>
      {/* Solution Scope Input */}
      <SectionCard
        title="3. Define Solution Scope"
        description="Provide key requirements or focus areas from the RFP to guide solution generation. AI will use real web search for current insights."
        icon={KeyRound}
        actionButton={
          <Button 
            onClick={onSynthesizeSolution} 
            disabled={isLoadingSolution || !solutionKeyPoints.trim()}
          >
            {isLoadingSolution ? (
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            ) : (
              <Combine className="mr-2 h-4 w-4" />
            )}
            Synthesize Solution Overview
          </Button>
        }
      >
        <div className="space-y-2">
          <Label htmlFor="solutionKeyPoints">Solution Key Points</Label>
          <Textarea
            id="solutionKeyPoints"
            value={solutionKeyPoints}
            onChange={(e) => onSolutionKeyPointsChange(e.target.value)}
            placeholder="Enter key requirements, technologies, or focus areas from the RFP..."
            className="min-h-[150px] text-sm"
            aria-label="Solution Key Points Input"
          />
        </div>
      </SectionCard>

      {/* Solution Overview Display */}
      {solutionOverviewAttempted && (
        <SectionCard
          title="4. Solution Overview"
          description="AI-generated solution overview and suggested OEM solutions. Editable text."
          icon={Combine}
          isLoading={isLoadingSolution}
        >
          {isLoadingSolution && !solutionOverview && (
            <div className="text-muted-foreground">Generating solution overview...</div>
          )}
          <div className="space-y-4">
            <div>
              <Label htmlFor="solutionOverviewText">Solution Overview Details</Label>
              <Textarea
                id="solutionOverviewText"
                value={solutionOverview}
                onChange={(e) => onSolutionOverviewChange(e.target.value)}
                placeholder="Generated solution overview will appear here..."
                className="min-h-[250px] text-sm bg-white"
                aria-label="Solution Overview Output"
                readOnly={isLoadingSolution}
              />
            </div>
            
            {oemSolutions.length > 0 && (
              <div className="mt-4">
                <Label>Suggested OEM Solutions</Label>
                <div className="mt-2 space-y-2">
                  {oemSolutions.map((solution, index) => (
                    <div key={index} className="p-3 bg-muted/50 rounded-md text-sm">
                      {solution}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </SectionCard>
      )}
    </>
  );
};
