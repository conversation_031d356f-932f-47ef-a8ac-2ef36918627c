"use client";

import React from "react";
import { ISuggestion } from "@/types/proposal";
import SuggestionItem from "./SuggestionItem";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

interface SuggestionsListProps {
  suggestions: ISuggestion[];
  selectedSuggestionIds: string[];
  onSuggestionSelect: (suggestionId: string) => void;
  className?: string;
}

const SuggestionsList: React.FC<SuggestionsListProps> = ({
  suggestions,
  selectedSuggestionIds,
  onSuggestionSelect,
  className,
}) => {
  if (!suggestions || suggestions.length === 0) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="text-lg">Improvement Suggestions</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground p-4">
            No suggestions available at the moment.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="text-lg">Improvement Suggestions</CardTitle>
      </CardHeader>
      <CardContent className="p-0">
        <ScrollArea className="h-72 w-full rounded-md ">
          <div className="space-y-0 p-4 pt-0">
            {suggestions.map((suggestion) => (
              <SuggestionItem
                key={suggestion.id}
                suggestion={suggestion}
                isSelected={selectedSuggestionIds.includes(suggestion.id)}
                onSelect={onSuggestionSelect}
              />
            ))}
          </div>
        </ScrollArea>
      </CardContent>
    </Card>
  );
};

export default SuggestionsList;
