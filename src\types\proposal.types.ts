// src/types/proposal.types.ts
import { z } from 'zod';


/**
 * Represents a single section within a proposal document.
 */
export interface IProposalSection {
  id: string; // Unique identifier for the section
  title: string; // Title of the section (e.g., "Introduction", "Methodology")
  content: string; // Generated content for this section
  order: number; // Order of this section in the document
  // Optional: specific prompts or keywords used to generate this section
  sourcePrompt?: string;
  sourceKeywords?: string[];
}

/**
 * Represents the complete proposal document.
 */
export interface IProposalDocument {
  id: string; // Unique identifier for the proposal
  title: string; // Overall title of the proposal
  clientId?: string; // Optional: Identifier for the client this proposal is for
  createdAt: string; // ISO date string of when the proposal was created
  updatedAt: string; // ISO date string of when the proposal was last updated
  sections: IProposalSection[]; // Array of sections in the proposal
  // Optional: metadata about the generation process
  generationMetadata?: {
    tone: string;
    length: string; // e.g., "short", "medium", "long"
    overallTopic: string;
  };
}

/**
 * Defines the structure for a request to generate an advanced proposal.
 */
export interface IAdvancedProposalRequest {
  topic: string; // Main topic or subject of the proposal
  clientNeeds: string; // Description of the client's requirements or pain points
  tone: 'formal' | 'informal' | 'technical' | 'persuasive'; // Desired tone of the proposal
  length: 'short' | 'medium' | 'long'; // Desired overall length of the proposal
  // Defines the sections to be included in the proposal.
  // If not provided, a default set of sections might be used.
  sections?: Array<{
    title: string; // Desired title for the section
    // Optional: specific instructions or keywords for this section
    specificInstructions?: string;
    researchKeywords?: string[]; // Keywords for Brave/Serper search to augment this section
  }>;
  // Optional: Custom prompt to override default generation for the entire proposal
  customPrompt?: string;
  // Optional: Identifier for a template to base the proposal on
  templateId?: string;
}

/**
 * Zod schema for validating IAdvancedProposalRequest.
 */
export const AdvancedProposalRequestSchema = z.object({
  topic: z.string().min(1, { message: 'Topic is required' }),
  clientNeeds: z.string().min(1, { message: 'Client needs are required' }),
  tone: z.enum(['formal', 'informal', 'technical', 'persuasive']),
  length: z.enum(['short', 'medium', 'long']),
  sections: z.array(z.object({
    title: z.string().min(1, { message: 'Section title is required' }),
    specificInstructions: z.string().optional(),
    researchKeywords: z.array(z.string()).optional(),
  })).optional(),
  customPrompt: z.string().optional(),
  templateId: z.string().optional(),
});
