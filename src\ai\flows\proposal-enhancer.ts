
// proposal-enhancer.ts
'use server';

/**
 * @fileOverview Enhances proposal sections by recommending improvements and additions based on generated content and RFP best practices.
 *
 * - enhanceProposal - A function that enhances proposal sections.
 * - EnhanceProposalInput - The input type for the enhanceProposal function.
 * - EnhanceProposalOutput - The return type for the enhanceProposal function.
 */

import {ai} from '@/ai/genkit';
import {z} from 'genkit';

const EnhanceProposalInputSchema = z.object({
  understandingRequirements: z
    .string()
    .describe('The generated "Understanding the Requirements" section.'),
  solutionOverview: z.string().describe('The generated "Solution Overview" section.'),
});
export type EnhanceProposalInput = z.infer<typeof EnhanceProposalInputSchema>;

const EnhanceProposalOutputSchema = z.object({
  recommendations: z
    .array(z.string())
    .describe('An array of recommended improvements and additions to the proposal.'),
});
export type EnhanceProposalOutput = z.infer<typeof EnhanceProposalOutputSchema>;

export async function enhanceProposal(input: EnhanceProposalInput): Promise<EnhanceProposalOutput> {
  return enhanceProposalFlow(input);
}

const enhanceProposalPrompt = ai.definePrompt({
  name: 'enhanceProposalPrompt',
  input: {schema: EnhanceProposalInputSchema},
  output: {schema: EnhanceProposalOutputSchema},
  prompt: `Given the following sections of a proposal, recommend improvements and additions based on best practices for RFPs. Focus on technical content, structure, and persuasiveness.

Understanding the Requirements:
{{{understandingRequirements}}}

Solution Overview:
{{{solutionOverview}}}

Provide specific, actionable recommendations to improve these sections.`,
});

const enhanceProposalFlow = ai.defineFlow(
  {
    name: 'enhanceProposalFlow',
    inputSchema: EnhanceProposalInputSchema,
    outputSchema: EnhanceProposalOutputSchema,
  },
  async input => {
    const {output} = await enhanceProposalPrompt(input);
    return output!;
  }
);
