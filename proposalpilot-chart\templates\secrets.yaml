apiVersion: v1
kind: Secret
metadata:
  name: {{ include "proposalpilot.fullname" . }}-secrets
type: Opaque
data:
  # API Keys
  gemini-api-key: {{ .Values.secrets.geminiApiKey | b64enc | quote }}
  brave-api-key: {{ .Values.secrets.braveApiKey | b64enc | quote }}
  serper-api-key: {{ .Values.secrets.serperApiKey | b64enc | quote }}
  # Database Credentials
  postgres-password: {{ .Values.secrets.postgresPassword | default "" | b64enc | quote }}
  postgres-postgres-password: {{ .Values.secrets.postgresPostgresPassword | default "" | b64enc | quote }}
