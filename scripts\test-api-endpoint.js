#!/usr/bin/env node

/**
 * Test the unified search engine via API endpoint
 */

async function testSearchAPI() {
  console.log('🧪 Testing Unified Search Engine API');
  console.log('====================================\n');

  try {
    const testQuery = 'modern web development best practices 2024';
    console.log(`🔍 Test Query: "${testQuery}"`);
    console.log('📡 Making API request...\n');

    const response = await fetch('http://localhost:9002/api/test-brave-search', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query: testQuery
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const result = await response.json();
    
    console.log('✅ API Response Received:');
    console.log('-------------------------');
    console.log(`🎯 Success: ${result.success}`);
    console.log(`📝 Query: ${result.query}`);
    console.log(`📊 Findings: ${result.findings?.length || 0} results`);
    console.log(`⏰ Timestamp: ${result.timestamp}`);
    
    if (result.findings && result.findings.length > 0) {
      console.log('\n📋 Sample Findings:');
      console.log('-------------------');
      result.findings.slice(0, 3).forEach((finding, index) => {
        console.log(`${index + 1}. ${finding.substring(0, 100)}...`);
      });
    }

    console.log('\n🎉 Unified Search Engine API Test Successful! ✅');
    
  } catch (error) {
    console.error('❌ API Test Failed:', error.message);
    
    if (error.message.includes('ECONNREFUSED')) {
      console.log('\n💡 Make sure the development server is running:');
      console.log('   npm run dev');
    }
  }
}

// Run the test
testSearchAPI();
