// src/app/api/generate-advanced-proposal/route.ts
import { NextResponse, NextRequest } from 'next/server';
import { AdvancedProposalService } from '@/services/ai/advancedProposalService';
import { IAdvancedProposalRequest, AdvancedProposalRequestSchema } from '@/types/proposal.types';
import { 
  ValidationError, 
  ApiError, 
  isApiError, 
  InternalServerError,
  ProposalGenerationError,
  ContextTooLargeError,
  InvalidTemplateError,
  GenerationTimeoutError
} from '@/lib/errors/api.errors';

/**
 * Handles the POST request for generating an advanced proposal
 */
export async function POST(request: NextRequest) {
  try {
    let body: unknown;
    
    // Parse and validate request body
    try {
      body = await request.json();
    } catch (error) {
      throw new ValidationError('Invalid JSON payload');
    }

    // Validate request body against schema
    const validationResult = AdvancedProposalRequestSchema.safeParse(body);

    if (!validationResult.success) {
      throw new ValidationError(
        'Invalid request data', 
        { errors: validationResult.error.format() }
      );
    }

    const advancedProposalRequest: IAdvancedProposalRequest = validationResult.data;

    try {
      // Call the service to generate the proposal
      const proposalDocument = await AdvancedProposalService.generate(advancedProposalRequest);
      return NextResponse.json(proposalDocument, { status: 200 });
    } catch (serviceError) {
      // Re-throw as ApiError if it's already an ApiError
      if (isApiError(serviceError)) {
        throw serviceError;
      }

      // Handle specific error cases
      const error = serviceError as Error;
      const errorMessage = error.message.toLowerCase();
      
      if (errorMessage.includes('context length')) {
        // Extract token counts from error message if available
        const maxMatch = errorMessage.match(/maximum context length is (\d+) tokens/);
        const actualMatch = errorMessage.match(/requested (\d+) tokens/);
        
        if (maxMatch && actualMatch) {
          const maxSize = parseInt(maxMatch[1], 10);
          const actualSize = parseInt(actualMatch[1], 10);
          throw new ContextTooLargeError(maxSize, actualSize);
        }
        throw new ContextTooLargeError(0, 0);
      }
      
      if (errorMessage.includes('timeout') || errorMessage.includes('timed out')) {
        const timeoutMatch = errorMessage.match(/(\d+)ms/);
        const timeoutMs = timeoutMatch ? parseInt(timeoutMatch[1], 10) : 30000; // Default 30s
        throw new GenerationTimeoutError(timeoutMs);
      }
      
      if (errorMessage.includes('template') && (errorMessage.includes('invalid') || errorMessage.includes('not found'))) {
        const templateId = advancedProposalRequest.templateId || 'unknown';
        throw new InvalidTemplateError(templateId, errorMessage);
      }
      
      // For other service errors, wrap in a more specific error type
      throw new ProposalGenerationError(
        'Failed to generate proposal', 
        process.env.NODE_ENV === 'development' ? { originalError: error.message } : undefined
      );
    }
  } catch (error) {
    console.error('Error in /api/generate-advanced-proposal:', error);

    // Handle known error types
    if (isApiError(error)) {
      return NextResponse.json(
        {
          error: {
            code: error.code,
            message: error.message,
            ...(error.details && { details: error.details })
          }
        },
        { status: error.statusCode }
      );
    }

    // Fallback for unexpected errors
    const unexpectedError = new InternalServerError(
      process.env.NODE_ENV === 'development' 
        ? (error as Error)?.message || 'An unexpected error occurred'
        : 'An unexpected error occurred'
    );

    return NextResponse.json(
      {
        error: {
          code: unexpectedError.code,
          message: unexpectedError.message,
          ...(process.env.NODE_ENV === 'development' && { 
            stack: (error as Error)?.stack 
          })
        }
      },
      { status: unexpectedError.statusCode }
    );
  }
}

// Add OPTIONS handler for CORS preflight requests
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 204,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
