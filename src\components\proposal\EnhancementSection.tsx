"use client";

import React, { lazy, Suspense } from 'react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { SectionCard } from './SectionCard';
import { Separator } from '@/components/ui/separator';
import { <PERSON>rk<PERSON>, Loader2 } from 'lucide-react';
import type { ISuggestion } from '@/types/proposal';

// Lazy load ProposalAmendmentSection for better performance
const ProposalAmendmentSection = lazy(() => import('./ProposalAmendmentSection').then(module => ({ default: module.ProposalAmendmentSection })));

interface EnhancementSectionProps {
  enhancementSuggestions: string[];
  amendmentInitialSuggestions: ISuggestion[];
  appliedSuggestions: ISuggestion[];
  currentProposalText: string;
  isLoadingEnhancements: boolean;
  onEnhanceProposal: () => void;
  onCurrentProposalTextChange: (value: string) => void;
  onApplySuggestion: (suggestion: ISuggestion) => void;
}

export const EnhancementSection: React.FC<EnhancementSectionProps> = ({
  enhancementSuggestions,
  amendmentInitialSuggestions,
  appliedSuggestions,
  currentProposalText,
  isLoadingEnhancements,
  onEnhanceProposal,
  onCurrentProposalTextChange,
  onApplySuggestion,
}) => {
  const hasProposalContent = currentProposalText.trim().length > 0;

  return (
    <SectionCard
      title="6. Enhance Proposal"
      description="Get AI-powered suggestions to improve your proposal content and structure."
      icon={Sparkles}
      actionButton={
        <Button
          onClick={onEnhanceProposal}
          disabled={isLoadingEnhancements || !hasProposalContent}
        >
          {isLoadingEnhancements ? (
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
          ) : (
            <Sparkles className="mr-2 h-4 w-4" />
          )}
          Enhance Proposal
        </Button>
      }
    >
      <div className="space-y-4">
        <div>
          <Label htmlFor="currentProposalText">Current Proposal Text</Label>
          <Textarea
            id="currentProposalText"
            value={currentProposalText}
            onChange={(e) => onCurrentProposalTextChange(e.target.value)}
            placeholder="Paste your current proposal text here for enhancement suggestions..."
            className="min-h-[200px] text-sm"
            aria-label="Current Proposal Text Input"
          />
        </div>

        {isLoadingEnhancements && (
          <div className="text-muted-foreground flex items-center">
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            Analyzing proposal and generating enhancement suggestions...
          </div>
        )}

        {enhancementSuggestions.length > 0 && (
          <div className="space-y-2">
            <Label>Enhancement Suggestions</Label>
            <div className="space-y-2">
              {enhancementSuggestions.map((suggestion, index) => (
                <div key={index} className="p-3 bg-blue-50 border border-blue-200 rounded-md dark:bg-blue-950/20 dark:border-blue-800">
                  <p className="text-sm text-blue-800 dark:text-blue-200">
                    {suggestion}
                  </p>
                </div>
              ))}
            </div>
          </div>
        )}

        {amendmentInitialSuggestions.length > 0 && (
          <>
            <Separator className="my-6" />
            <Suspense fallback={
              <div className="flex items-center justify-center p-4">
                <Loader2 className="h-4 w-4 animate-spin" />
                <span className="ml-2">Loading amendment suggestions...</span>
              </div>
            }>
              <ProposalAmendmentSection
                initialProposalText={currentProposalText}
                initialSuggestions={amendmentInitialSuggestions}
                appliedSuggestions={appliedSuggestions}
                onAppliedSuggestionsChange={(suggestions) => {
                  // Apply the last suggestion from the array
                  if (suggestions.length > appliedSuggestions.length) {
                    const newSuggestion = suggestions[suggestions.length - 1];
                    onApplySuggestion(newSuggestion);
                  }
                }}
                onProposalTextChange={onCurrentProposalTextChange}
              />
            </Suspense>
          </>
        )}
      </div>
    </SectionCard>
  );
};
