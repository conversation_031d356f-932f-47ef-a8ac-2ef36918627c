"use client";

import React from 'react';
import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardHeader, CardTitle } from '@/components/ui/card';
import { Loader2 } from 'lucide-react';

interface SectionCardProps {
  title: string;
  description: string;
  icon: React.ElementType;
  children: React.ReactNode;
  actionButton?: React.ReactNode;
  isLoading?: boolean;
  className?: string;
}

export const SectionCard: React.FC<SectionCardProps> = ({ 
  title, 
  description, 
  icon: Icon, 
  children, 
  actionButton, 
  isLoading,
  className = ""
}) => (
  <Card className={`shadow-lg ${className}`}>
    <CardHeader>
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <Icon className="w-7 h-7 text-primary" />
          <div>
            <CardTitle className="text-xl">{title}</CardTitle>
            <CardDescription>{description}</CardDescription>
          </div>
        </div>
        {isLoading && <Loader2 className="h-5 w-5 animate-spin text-primary" />}
      </div>
    </CardHeader>
    <CardContent className="space-y-4">
      {children}
    </CardContent>
    {actionButton && (
      <CardFooter>
        {actionButton}
      </CardFooter>
    )}
  </Card>
);
