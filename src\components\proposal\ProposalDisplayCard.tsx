import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';

interface IProposalDisplayCardProps {
  proposalText: string;
  title?: string;
}

const ProposalDisplayCard: React.FC<IProposalDisplayCardProps> = ({ proposalText, title = 'Proposal' }) => {
  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>{title}</CardTitle>
      </CardHeader>
      <CardContent>
        <Textarea
          readOnly
          value={proposalText}
          className="h-64 resize-none border-none focus-visible:ring-0 focus-visible:ring-offset-0"
          placeholder="Proposal text will appear here."
        />
      </CardContent>
    </Card>
  );
};

export default ProposalDisplayCard;
