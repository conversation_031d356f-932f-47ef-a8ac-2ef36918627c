
// This is an autogenerated file from Firebase Studio.
'use server';
/**
 * @fileOverview This file contains the Genkit flow for generating the 'Understanding the Requirements' section of a proposal based on RFP content.
 *
 * - analyzeRequirements - A function that handles the analysis of RFP requirements.
 * - AnalyzeRequirementsInput - The input type for the analyzeRequirements function.
 * - AnalyzeRequirementsOutput - The return type for the analyzeRequirements function.
 */

import {ai} from '@/ai/genkit';
import {z} from 'genkit';
import { UserPreferencesSchema } from '../schemas/user-preferences';


const AnalyzeRequirementsTextInputSchema = z.object({
  inputType: z.literal("text"),
  rfpContent: z.string().describe('The content of the Request for Proposal (RFP) as text.'),
  userPreferences: UserPreferencesSchema.optional().describe('User preferences and considerations to guide analysis'),
});

const AnalyzeRequirementsPdfInputSchema = z.object({
  inputType: z.literal("pdf"),
  rfpPdfDataUri: z.string().describe("A PDF of the RFP, as a data URI that must include a MIME type and use Base64 encoding. Expected format: 'data:application/pdf;base64,<encoded_data>'."),
  userPreferences: UserPreferencesSchema.optional().describe('User preferences and considerations to guide analysis'),
});

const AnalyzeRequirementsInputSchema = z.discriminatedUnion("inputType", [
  AnalyzeRequirementsTextInputSchema,
  AnalyzeRequirementsPdfInputSchema,
]);

export type AnalyzeRequirementsInput = z.infer<typeof AnalyzeRequirementsInputSchema>;

const AnalyzeRequirementsOutputSchema = z.object({
  understandingOfTheRequirements: z.string().describe('The generated Understanding the Requirements section of the proposal, focusing on technical aspects.'),
  // Enhanced output with structured sub-sections for better context passing
  functionalRequirements: z.array(z.string()).describe('List of key functional requirements extracted from the RFP.'),
  nonFunctionalRequirements: z.array(z.string()).describe('List of non-functional requirements (performance, security, scalability, etc.).'),
  technicalConstraints: z.array(z.string()).describe('Technical constraints and limitations mentioned in the RFP.'),
  integrationRequirements: z.array(z.string()).describe('Integration points and external system requirements.'),
  complianceRequirements: z.array(z.string()).describe('Compliance, regulatory, and standards requirements.'),
});
export type AnalyzeRequirementsOutput = z.infer<typeof AnalyzeRequirementsOutputSchema>;

export async function analyzeRequirements(input: AnalyzeRequirementsInput): Promise<AnalyzeRequirementsOutput> {
  return analyzeRequirementsFlow(input);
}

const analyzeRequirementsPrompt = ai.definePrompt({
  name: 'analyzeRequirementsPrompt',
  // Input schema for the prompt itself will always be text, after PDF processing
  input: { schema: z.object({
    rfpContent: z.string(),
    userPreferences: UserPreferencesSchema.optional()
  }) },
  output: {schema: AnalyzeRequirementsOutputSchema},
  prompt: `You are an AI assistant specialized in analyzing Request for Proposals (RFPs) for software/solution related projects.
  Your task is to generate a comprehensive 'Understanding the Requirements' section with detailed sub-sections and structured analysis.

  **Focus strictly on the technical aspects.** Extract key technical requirements, technical objectives, specific technical instructions, functionalities, performance criteria, integration points, technology stack preferences (if any), and any technical constraints mentioned in the RFP.
  **Exclude administrative details** such as submission deadlines, proposal format requirements, contact persons, procurement timelines, legal clauses, pricing instructions, or company background requests.

  RFP Content: {{{rfpContent}}}

  {{#if userPreferences}}
  **USER PREFERENCES & CONSIDERATIONS:**
  {{#if userPreferences.preferredTechnologies}}
  - **Preferred Technologies:** {{#each userPreferences.preferredTechnologies}}{{this}}{{#unless @last}}, {{/unless}}{{/each}}
  {{/if}}
  {{#if userPreferences.technicalConstraints}}
  - **Technical Constraints:** {{#each userPreferences.technicalConstraints}}{{this}}{{#unless @last}}, {{/unless}}{{/each}}
  {{/if}}
  {{#if userPreferences.budgetConsiderations}}
  - **Budget Considerations:** {{userPreferences.budgetConsiderations}}
  {{/if}}
  {{#if userPreferences.timelineConstraints}}
  - **Timeline Constraints:** {{userPreferences.timelineConstraints}}
  {{/if}}
  {{#if userPreferences.complianceRequirements}}
  - **Compliance Requirements:** {{#each userPreferences.complianceRequirements}}{{this}}{{#unless @last}}, {{/unless}}{{/each}}
  {{/if}}
  {{#if userPreferences.integrationRequirements}}
  - **Integration Requirements:** {{#each userPreferences.integrationRequirements}}{{this}}{{#unless @last}}, {{/unless}}{{/each}}
  {{/if}}
  {{#if userPreferences.scalabilityRequirements}}
  - **Scalability Requirements:** {{userPreferences.scalabilityRequirements}}
  {{/if}}
  {{#if userPreferences.securityRequirements}}
  - **Security Requirements:** {{#each userPreferences.securityRequirements}}{{this}}{{#unless @last}}, {{/unless}}{{/each}}
  {{/if}}
  {{#if userPreferences.additionalConsiderations}}
  - **Additional Considerations:** {{userPreferences.additionalConsiderations}}
  {{/if}}

  **IMPORTANT:** Consider these user preferences when analyzing the RFP. Incorporate them into your analysis where relevant, and ensure they are reflected in the extracted requirements arrays.
  {{/if}}

  **IMPORTANT OUTPUT FORMAT:**
  You must return a JSON object with exactly this structure:
  {
    "understandingOfTheRequirements": "Your comprehensive requirements analysis in Markdown format with sub-sections...",
    "functionalRequirements": ["Requirement 1", "Requirement 2", "..."],
    "nonFunctionalRequirements": ["Performance requirement", "Security requirement", "..."],
    "technicalConstraints": ["Constraint 1", "Constraint 2", "..."],
    "integrationRequirements": ["Integration 1", "Integration 2", "..."],
    "complianceRequirements": ["Compliance 1", "Compliance 2", "..."]
  }

  **For the understandingOfTheRequirements field:**
  - Format as comprehensive Markdown with the following sub-sections:
    - **Business Context & Objectives**
    - **Functional Requirements**
    - **Non-Functional Requirements**
    - **Technical Constraints & Limitations**
    - **Integration Requirements**
    - **Compliance & Regulatory Requirements**
    - **Success Criteria & Acceptance Criteria**
  - Each sub-section should be detailed and demonstrate deep technical understanding
  - Use bullet points, numbered lists, and emphasis for clarity
  - Aim for 1500-2000 words total to provide comprehensive coverage
  - **Incorporate user preferences where relevant** in each section

  **For the structured arrays:**
  - Extract specific, actionable requirements into the respective arrays
  - These will be used by subsequent agents for solution design
  - Be specific and technical in your extractions
  - **Include user preferences as additional requirements** where appropriate
  `,config: {
    safetySettings: [
      {
        category: 'HARM_CATEGORY_DANGEROUS_CONTENT',
        threshold: 'BLOCK_ONLY_HIGH',
      },
            {
        category: 'HARM_CATEGORY_HARASSMENT',
        threshold: 'BLOCK_MEDIUM_AND_ABOVE',
      },
            {
        category: 'HARM_CATEGORY_HATE_SPEECH',
        threshold: 'BLOCK_MEDIUM_AND_ABOVE',
      },
    ],
  },
});

const analyzeRequirementsFlow = ai.defineFlow(
  {
    name: 'analyzeRequirementsFlow',
    inputSchema: AnalyzeRequirementsInputSchema,
    outputSchema: AnalyzeRequirementsOutputSchema,
  },
  async (input) => {
    let rfpTextContent = '';

    if (input.inputType === 'pdf') {
      try {
        const PdfReader = (await import('pdfreader')).PdfReader;
        const base64Data = input.rfpPdfDataUri.split(',')[1];
        if (!base64Data) {
          throw new Error('Invalid PDF data URI format.');
        }
        const pdfBuffer = Buffer.from(base64Data, 'base64');

        const texts: string[] = [];
        await new Promise<void>((resolve, reject) => {
          new PdfReader().parseBuffer(pdfBuffer, (err, item) => {
            if (err) {
              return reject(err);
            }
            if (!item) { // EOF
              return resolve();
            }
            if ('text' in item && item.text) {
              texts.push(item.text);
            }
          });
        });
        rfpTextContent = texts.join(' ');

      } catch (error: any) {
        console.error('Failed to parse PDF with pdfreader:', error);
        throw new Error(`Failed to process PDF: ${error.message || 'Unknown PDF parsing error with pdfreader'}`);
      }
    } else {
      rfpTextContent = input.rfpContent;
    }

    if (!rfpTextContent.trim()) {
        throw new Error('Extracted RFP content is empty. Cannot analyze.');
    }

    try {
      console.log('Requirements Analyzer Input:', JSON.stringify({
        rfpContentLength: rfpTextContent.length,
        userPreferences: input.userPreferences
      }, null, 2));

      const {output} = await analyzeRequirementsPrompt({
        rfpContent: rfpTextContent,
        userPreferences: input.userPreferences
      });

      console.log('Requirements Analyzer Output:', JSON.stringify(output, null, 2));

      if (!output) {
        throw new Error('AI failed to generate requirements analysis.');
      }

      return output;
    } catch (error) {
      console.error('Error in requirements analyzer flow:', error);
      throw error;
    }
  }
);

