
'use server';

import type { AnalyzeRequirementsInput, AnalyzeRequirementsOutput } from '@/ai/flows/requirements-analyzer';
import { analyzeRequirements as analyzeRequirementsFlow } from '@/ai/flows/requirements-analyzer';

import type { SolutionOverviewInput, SolutionOverviewOutput } from '@/ai/flows/solution-synthesizer';
import { generateSolutionOverview as generateSolutionOverviewFlow } from '@/ai/flows/solution-synthesizer';

import type { EnhanceProposalInput, EnhanceProposalOutput } from '@/ai/flows/proposal-enhancer';
import { enhanceProposal as enhanceProposalFlow } from '@/ai/flows/proposal-enhancer';

import type { GenerateArchitectureDiagramInput, GenerateArchitectureDiagramOutput } from '@/ai/flows/architecture-visualizer';
import { generateArchitectureDiagram as generateArchitectureDiagramFlow } from '@/ai/flows/architecture-visualizer';

import type { TechnicalImplementationInput, TechnicalImplementationOutput } from '@/ai/flows/technical-implementation';
import { generateTechnicalImplementation as generateTechnicalImplementationFlow } from '@/ai/flows/technical-implementation';

import type { ProposalOrchestratorInput, ProposalOrchestratorOutput, ProposalContext } from '@/ai/flows/proposal-orchestrator';
import { generateComprehensiveProposal as generateComprehensiveProposalFlow } from '@/ai/flows/proposal-orchestrator';

import { unifiedSearchEngine, type SearchSummary } from '@/lib/unified-search-engine';


export async function analyzeRequirementsAction(input: AnalyzeRequirementsInput): Promise<AnalyzeRequirementsOutput> {
  try {
    const result = await analyzeRequirementsFlow(input);
    return result;
  } catch (error: any) {
    console.error("Error in analyzeRequirementsAction:", error);
    // Propagate the specific error message from the flow if available
    throw new Error(error.message || "Failed to analyze requirements. Please try again.");
  }
}

export async function generateSolutionOverviewAction(input: SolutionOverviewInput): Promise<SolutionOverviewOutput> {
  try {
    const result = await generateSolutionOverviewFlow(input);
    return result;
  } catch (error: any) {
    console.error("Error in generateSolutionOverviewAction:", error);
    throw new Error("Failed to generate solution overview. Please try again.");
  }
}

export async function enhanceProposalAction(input: EnhanceProposalInput): Promise<EnhanceProposalOutput> {
  try {
    const result = await enhanceProposalFlow(input);
    return result;
  } catch (error: any) {
    console.error("Error in enhanceProposalAction:", error);
    throw new Error("Failed to enhance proposal. Please try again.");
  }
}

export async function performResearchAction(query: string): Promise<SearchSummary> {
  try {
    if (!query || query.trim().length === 0) {
      throw new Error("Research query cannot be empty");
    }

    const result = await unifiedSearchEngine.search(query, {
      maxResults: 10,
      includeKnowledgeGraph: true,
      timeout: 20000,
      parallelExecution: true,
      maxProviders: 3,
      includeBackups: true
    });

    return result;
  } catch (error: any) {
    console.error("Error in performResearchAction:", error);
    throw new Error(`Failed to perform research: ${error.message || "Please try again."}`);
  }
}

export async function generateArchitectureDiagramAction(input: GenerateArchitectureDiagramInput): Promise<GenerateArchitectureDiagramOutput> {
  try {
    const result = await generateArchitectureDiagramFlow(input);
    return result;
  } catch (error: any) {
    console.error("Error in generateArchitectureDiagramAction:", error);
    throw new Error(error.message || "Failed to generate architecture diagram. Please try again.");
  }
}

export async function generateTechnicalImplementationAction(input: TechnicalImplementationInput): Promise<TechnicalImplementationOutput> {
  try {
    const result = await generateTechnicalImplementationFlow(input);
    return result;
  } catch (error: any) {
    console.error("Error in generateTechnicalImplementationAction:", error);
    throw new Error(error.message || "Failed to generate technical implementation. Please try again.");
  }
}

export async function generateComprehensiveProposalAction(
  input: ProposalOrchestratorInput
): Promise<ProposalOrchestratorOutput> {
  try {
    const result = await generateComprehensiveProposalFlow(input);
    return result;
  } catch (error: any) {
    console.error("Error in generateComprehensiveProposalAction:", error);
    throw new Error(error.message || "Failed to generate comprehensive proposal. Please try again.");
  }
}
