/**
 * @fileOverview Integration test utilities for Brave Search MCP
 * 
 * This module provides test functions to verify the MCP integration
 * without requiring actual API calls.
 */

import { performWebResearch } from '@/ai/tools/web-researcher';

/**
 * Test the web research tool with a sample query
 */
export async function testWebResearchTool(): Promise<{
  success: boolean;
  message: string;
  findings?: string[];
  error?: string;
}> {
  try {
    console.log('Testing web research tool...');
    
    const testQuery = 'cloud native architecture best practices';
    const result = await performWebResearch({ query: testQuery });
    
    if (result.findings && result.findings.length > 0) {
      return {
        success: true,
        message: 'Web research tool is working correctly',
        findings: result.findings,
      };
    } else {
      return {
        success: false,
        message: 'Web research tool returned no findings',
        findings: [],
      };
    }
  } catch (error) {
    return {
      success: false,
      message: 'Web research tool failed',
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

/**
 * Check if the Brave API key is configured
 */
export function checkBraveApiKey(): {
  configured: boolean;
  message: string;
} {
  const apiKey = process.env.BRAVE_API_KEY;
  
  if (!apiKey) {
    return {
      configured: false,
      message: 'BRAVE_API_KEY not found in environment variables. The system will fall back to simulated research.',
    };
  }
  
  if (apiKey.includes('your_') || apiKey.length < 10) {
    return {
      configured: false,
      message: 'BRAVE_API_KEY appears to be a placeholder. Please add your actual API key.',
    };
  }
  
  return {
    configured: true,
    message: 'BRAVE_API_KEY is configured and appears valid.',
  };
}

/**
 * Run a comprehensive integration test
 */
export async function runIntegrationTest(): Promise<{
  overall: boolean;
  apiKeyStatus: ReturnType<typeof checkBraveApiKey>;
  webResearchTest: Awaited<ReturnType<typeof testWebResearchTool>>;
}> {
  console.log('Running ProposalPilot Brave Search integration test...\n');
  
  const apiKeyStatus = checkBraveApiKey();
  console.log('API Key Status:', apiKeyStatus.message);
  
  const webResearchTest = await testWebResearchTool();
  console.log('Web Research Test:', webResearchTest.message);
  
  if (webResearchTest.findings) {
    console.log('Sample findings:');
    webResearchTest.findings.forEach((finding, index) => {
      console.log(`${index + 1}. ${finding.substring(0, 100)}...`);
    });
  }
  
  const overall = webResearchTest.success;
  
  return {
    overall,
    apiKeyStatus,
    webResearchTest,
  };
}
