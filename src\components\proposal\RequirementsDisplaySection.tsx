"use client";

import React from 'react';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { SectionCard } from './SectionCard';
import { Lightbulb } from 'lucide-react';

interface RequirementsDisplaySectionProps {
  understandingReqs: string;
  requirementsAttempted: boolean;
  isLoadingRequirements: boolean;
  onUnderstandingReqsChange: (value: string) => void;
}

export const RequirementsDisplaySection: React.FC<RequirementsDisplaySectionProps> = ({
  understandingReqs,
  requirementsAttempted,
  isLoadingRequirements,
  onUnderstandingReqsChange,
}) => {
  if (!requirementsAttempted) {
    return null;
  }

  return (
    <SectionCard
      title="2. Understanding the Requirements"
      description="AI-generated analysis of the RFP requirements. Editable text."
      icon={Lightbulb}
      isLoading={isLoadingRequirements}
    >
      {isLoadingRequirements && !understandingReqs && (
        <div className="text-muted-foreground">Analyzing requirements...</div>
      )}
      <div className="space-y-2">
        <Label htmlFor="understandingReqsText">Requirements Analysis</Label>
        <Textarea
          id="understandingReqsText"
          value={understandingReqs}
          onChange={(e) => onUnderstandingReqsChange(e.target.value)}
          placeholder="Generated requirements analysis will appear here..."
          className="min-h-[250px] text-sm bg-white"
          aria-label="Requirements Analysis Output"
          readOnly={isLoadingRequirements}
        />
      </div>
    </SectionCard>
  );
};
