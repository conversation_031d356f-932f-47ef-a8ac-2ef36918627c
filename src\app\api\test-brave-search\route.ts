/**
 * @fileOverview API route to test Brave Search MCP integration
 */

import { NextRequest, NextResponse } from 'next/server';
import { runIntegrationTest } from '@/lib/test-integration';

export async function GET(request: NextRequest) {
  try {
    const testResults = await runIntegrationTest();
    
    return NextResponse.json({
      success: testResults.overall,
      timestamp: new Date().toISOString(),
      results: testResults,
    });
  } catch (error) {
    console.error('Integration test failed:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString(),
    }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const { query } = await request.json();
    
    if (!query || typeof query !== 'string') {
      return NextResponse.json({
        success: false,
        error: 'Query parameter is required and must be a string',
      }, { status: 400 });
    }
    
    // Import the web research tool dynamically to avoid server-side issues
    const { performWebResearch } = await import('@/ai/tools/web-researcher');
    
    const result = await performWebResearch({ query });
    
    return NextResponse.json({
      success: true,
      query,
      findings: result.findings,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Web research test failed:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString(),
    }, { status: 500 });
  }
}
