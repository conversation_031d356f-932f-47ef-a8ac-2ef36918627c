// jest.setup.js
require('@testing-library/jest-dom');

// Mock Next.js router
jest.mock('next/navigation', () => ({
  useRouter() {
    return {
      push: jest.fn(),
      replace: jest.fn(),
      prefetch: jest.fn(),
      back: jest.fn(),
      forward: jest.fn(),
      refresh: jest.fn(),
    };
  },
  useSearchParams() {
    return new URLSearchParams();
  },
  usePathname() {
    return '/';
  },
}));

// Mock Next.js themes
jest.mock('next-themes', () => ({
  useTheme: () => ({
    theme: 'light',
    setTheme: jest.fn(),
    resolvedTheme: 'light',
  }),
  ThemeProvider: ({ children }) => children,
}));

// Mock window.matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(), // deprecated
    removeListener: jest.fn(), // deprecated
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
});

// Mock ResizeObserver
global.ResizeObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));

// Mock IntersectionObserver
global.IntersectionObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));

// Mock DOMPurify
jest.mock('dompurify', () => ({
  sanitize: jest.fn((input) => input), // Return input as-is for tests
}));

// Mock lucide-react icons
jest.mock('lucide-react', () => ({
  AlertTriangle: () => 'AlertTriangle',
  RefreshCw: () => 'RefreshCw',
  Home: () => 'Home',
  Loader2: () => 'Loader2',
  FileText: () => 'FileText',
  Lightbulb: () => 'Lightbulb',
  UploadCloud: () => 'UploadCloud',
  FileUp: () => 'FileUp',
  CircleX: () => 'CircleX',
  KeyRound: () => 'KeyRound',
  Zap: () => 'Zap',
  CheckCircle: () => 'CheckCircle',
  Clock: () => 'Clock',
  ExternalLink: () => 'ExternalLink',
  Maximize: () => 'Maximize',
  Minimize: () => 'Minimize',
  WandSparkles: () => 'WandSparkles',
}));

// Suppress console errors in tests unless explicitly testing error scenarios
const originalError = console.error;
beforeAll(() => {
  console.error = (...args) => {
    if (
      typeof args[0] === 'string' &&
      args[0].includes('Warning: ReactDOM.render is no longer supported')
    ) {
      return;
    }
    originalError.call(console, ...args);
  };
});

afterAll(() => {
  console.error = originalError;
});

// Global test timeout
jest.setTimeout(10000);
