# Brave Search MCP Integration Setup

This guide will help you set up real web searching capabilities in ProposalPilot using the Brave Search MCP (Model Context Protocol) integration.

## Prerequisites

1. **Node.js** (version 18 or higher)
2. **npm** or **yarn** package manager
3. **Brave Search API Key** (free tier available)

## Step 1: Get Your Brave Search API Key

1. Visit the [Brave Search API website](https://brave.com/search/api/)
2. Sign up for a free account
3. Choose a plan:
   - **Free Tier**: 2,000 queries per month
   - **Paid Plans**: Higher query limits available
4. Go to the [API Dashboard](https://api-dashboard.search.brave.com/app/keys)
5. Generate your API key

## Step 2: Configure Environment Variables

1. Copy the example environment file:
   ```bash
   cp .env.local.example .env.local
   ```

2. Edit `.env.local` and add your Brave Search API key:
   ```env
   BRAVE_API_KEY=your_actual_brave_search_api_key_here
   ```

3. Make sure `.env.local` is in your `.gitignore` file (it should be by default)

## Step 3: Install Dependencies

The required MCP SDK should already be installed. If not, run:

```bash
npm install @modelcontextprotocol/sdk
```

## Step 4: Test the Integration

1. Start your development server:
   ```bash
   npm run dev
   ```

2. Try generating a proposal with web research:
   - Upload an RFP document
   - Click "Generate Solution Overview"
   - The system will now use real web search instead of simulated research

## How It Works

### MCP Architecture

The integration uses the Model Context Protocol (MCP) to communicate with the Brave Search API:

1. **MCP Server**: Runs the official Brave Search MCP server (`@modelcontextprotocol/server-brave-search`)
2. **MCP Client**: Our custom client (`src/lib/mcp-client.ts`) communicates with the server
3. **Web Research Tool**: Updated tool (`src/ai/tools/web-researcher.ts`) uses real search results

### Search Capabilities

- **Web Search**: General web search with pagination and filtering
- **Local Search**: Business and location-based search (with automatic fallback to web search)
- **Smart Fallbacks**: Graceful degradation when API is unavailable

### Features

- **Real-time Results**: Live web search results from Brave Search
- **Fallback Mechanism**: Falls back to simulated research if API key is missing
- **Error Handling**: Robust error handling with informative messages
- **Rate Limiting**: Respects Brave Search API limits (max 20 results per query)

## Troubleshooting

### Common Issues

1. **"BRAVE_API_KEY not found" Warning**
   - Ensure your `.env.local` file exists and contains the API key
   - Restart your development server after adding the key

2. **"MCP connection failed" Error**
   - Check that the MCP server package is installed: `npm list @modelcontextprotocol/sdk`
   - Verify your API key is valid by testing it directly with Brave Search API

3. **"Web research temporarily unavailable" Message**
   - This indicates a temporary API issue or network problem
   - The system will fall back to generic recommendations

4. **No Search Results**
   - Try refining your search query to be more specific
   - Check if your API key has remaining quota

### Debug Mode

To enable debug logging, add this to your `.env.local`:

```env
DEBUG=mcp:*
```

### API Limits

- **Free Tier**: 2,000 queries/month
- **Rate Limits**: Built-in throttling (1 second between requests)
- **Result Limits**: Max 20 results per query, max 9 offset for pagination
- **Fallback System**: Intelligent fallbacks when rate limits are exceeded

For detailed rate limiting information, see [Rate Limiting Guide](rate-limiting-guide.md).

## Advanced Configuration

### Custom Search Parameters

You can modify the search behavior in `src/lib/mcp-client.ts`:

- `count`: Number of results (1-20)
- `offset`: Pagination offset (0-9)
- Search filters and safety levels (see Brave Search API docs)

### Alternative MCP Servers

If you prefer a different search provider, you can:

1. Replace the MCP server command in `src/lib/mcp-client.ts`
2. Update the tool names and parameters accordingly
3. Modify the response parsing logic

## Security Notes

- **Never commit your API key** to version control
- **Use environment variables** for all sensitive configuration
- **Monitor your API usage** to avoid unexpected charges
- **Rotate your API keys** regularly for security

## Support

- [Brave Search API Documentation](https://brave.com/search/api/)
- [Model Context Protocol Documentation](https://modelcontextprotocol.io/)
- [ProposalPilot Issues](https://github.com/your-repo/issues) (if applicable)

## Next Steps

With real web search enabled, your ProposalPilot will now:

1. **Generate more accurate proposals** with current market insights
2. **Include up-to-date technology trends** in solution overviews
3. **Provide relevant industry best practices** from recent sources
4. **Enhance competitive analysis** with real-time information

The web research tool will automatically be used by the AI when generating solution overviews and enhancements, providing your proposals with current, relevant information from the web.
