// src/app/api/proposal/amend/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { amendProposalFlow } from '@/ai/flows/amendProposalFlow';
import { runFlow } from '@genkit-ai/flow';
import { amendRequestSchema } from '@/ai/schemas/amendmentSchemas'; // Assuming this path is correct based on Next.js conventions (@/ refers to src/)
import { ZodError } from 'zod';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // Validate request body
    const validationResult = amendRequestSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid request body', details: validationResult.error.flatten() },
        { status: 400 }
      );
    }

    const validatedData = validationResult.data;

    // Call the Genkit flow
    const result = await runFlow(amendProposalFlow, validatedData);

    // Return the full response including amended text and applied suggestions
    return NextResponse.json(result, { status: 200 });

  } catch (error) {
    console.error('Error in /api/proposal/amend:', error);

    if (error instanceof ZodError) {
      // This case should ideally be caught by safeParse, but as a fallback
      return NextResponse.json(
        { error: 'Invalid request body', details: error.flatten() },
        { status: 400 }
      );
    }
    
    let errorMessage = 'Failed to amend proposal due to an internal server error.';
    if (error instanceof Error) {
        // Avoid exposing raw internal error messages unless they are known to be safe
        // For now, logging the specific message and returning a generic one
        console.error('Specific error message:', error.message);
    }

    return NextResponse.json(
      { error: 'An error occurred while processing your request.'},
      { status: 500 }
    );
  }
}
