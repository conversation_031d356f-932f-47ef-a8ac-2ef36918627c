"use client";

import { useState, useCallback } from 'react';
import type { ISuggestion } from '@/types/proposal';

export interface IUserPreferences {
  preferredTechnologies?: string[];
  technicalConstraints?: string[];
  budgetConsiderations?: string;
  timelineConstraints?: string;
  complianceRequirements?: string[];
  integrationRequirements?: string[];
  scalabilityRequirements?: string;
  securityRequirements?: string[];
  additionalConsiderations?: string;
}

export interface ProposalState {
  // RFP Input
  rfpContent: string;
  rfpPdfDataUri: string | null;
  uploadedFileName: string | null;
  
  // Generated Content
  understandingReqs: string;
  solutionKeyPoints: string;
  solutionOverview: string;
  oemSolutions: string[];
  technicalImplementation: string;
  comprehensiveProposal: string;
  currentProposalText: string;
  
  // Diagram
  mermaidScript: string;
  mermaidSvg: string;
  architectureInput: string;
  diagramType: 'conceptual' | 'reference';
  
  // Enhancement
  enhancementSuggestions: string[];
  amendmentInitialSuggestions: ISuggestion[];
  appliedSuggestions: ISuggestion[];
  
  // Loading States
  isLoadingRequirements: boolean;
  isLoadingSolution: boolean;
  isLoadingEnhancements: boolean;
  isLoadingDiagram: boolean;
  isLoadingComprehensive: boolean;
  
  // Comprehensive Proposal Progress
  comprehensiveProgress: number;
  comprehensiveCurrentStep: string;
  
  // UI State
  understandingReqsAttempted: boolean;
  solutionOverviewAttempted: boolean;
  showComprehensiveMode: boolean;
  
  // User Preferences
  preferredTechnologies: string;
  technicalConstraints: string;
  budgetConsiderations: string;
  timelineConstraints: string;
  complianceRequirements: string;
  integrationRequirements: string;
  scalabilityRequirements: string;
  securityRequirements: string;
  additionalConsiderations: string;
}

const initialMermaidScript = `graph TD;
    A[Client's Core Problem] -->|Identified Via RFP| B(Proposed Solution);
    B --> C{Feature 1: Requirement Analysis};
    B --> D{Feature 2: Solution Synthesis};
    C --> E[Key Benefit A];
    D --> F[Key Benefit B];
    B --> G((OEM Integration Suggestion));
    G -.-> H[OEM Partner X];
    G -.-> I[OEM Partner Y];`;

export const useProposalState = () => {
  const [state, setState] = useState<ProposalState>({
    // RFP Input
    rfpContent: '',
    rfpPdfDataUri: null,
    uploadedFileName: null,
    
    // Generated Content
    understandingReqs: '',
    solutionKeyPoints: '',
    solutionOverview: '',
    oemSolutions: [],
    technicalImplementation: '',
    comprehensiveProposal: '',
    currentProposalText: '',
    
    // Diagram
    mermaidScript: initialMermaidScript,
    mermaidSvg: '',
    architectureInput: '',
    diagramType: 'conceptual',
    
    // Enhancement
    enhancementSuggestions: [],
    amendmentInitialSuggestions: [],
    appliedSuggestions: [],
    
    // Loading States
    isLoadingRequirements: false,
    isLoadingSolution: false,
    isLoadingEnhancements: false,
    isLoadingDiagram: false,
    isLoadingComprehensive: false,
    
    // Comprehensive Proposal Progress
    comprehensiveProgress: 0,
    comprehensiveCurrentStep: '',
    
    // UI State
    understandingReqsAttempted: false,
    solutionOverviewAttempted: false,
    showComprehensiveMode: false,
    
    // User Preferences
    preferredTechnologies: '',
    technicalConstraints: '',
    budgetConsiderations: '',
    timelineConstraints: '',
    complianceRequirements: '',
    integrationRequirements: '',
    scalabilityRequirements: '',
    securityRequirements: '',
    additionalConsiderations: '',
  });

  const updateState = useCallback((updates: Partial<ProposalState>) => {
    setState(prev => ({ ...prev, ...updates }));
  }, []);

  const buildUserPreferences = useCallback((): IUserPreferences | undefined => {
    const preferences: Partial<IUserPreferences> = {};

    if (state.preferredTechnologies.trim()) {
      preferences.preferredTechnologies = state.preferredTechnologies.split(',').map(tech => tech.trim()).filter(tech => tech);
    }
    if (state.technicalConstraints.trim()) {
      preferences.technicalConstraints = state.technicalConstraints.split(',').map(constraint => constraint.trim()).filter(constraint => constraint);
    }
    if (state.budgetConsiderations.trim()) {
      preferences.budgetConsiderations = state.budgetConsiderations.trim();
    }
    if (state.timelineConstraints.trim()) {
      preferences.timelineConstraints = state.timelineConstraints.trim();
    }
    if (state.complianceRequirements.trim()) {
      preferences.complianceRequirements = state.complianceRequirements.split(',').map(req => req.trim()).filter(req => req);
    }
    if (state.integrationRequirements.trim()) {
      preferences.integrationRequirements = state.integrationRequirements.split(',').map(req => req.trim()).filter(req => req);
    }
    if (state.scalabilityRequirements.trim()) {
      preferences.scalabilityRequirements = state.scalabilityRequirements.trim();
    }
    if (state.securityRequirements.trim()) {
      preferences.securityRequirements = state.securityRequirements.split(',').map(req => req.trim()).filter(req => req);
    }
    if (state.additionalConsiderations.trim()) {
      preferences.additionalConsiderations = state.additionalConsiderations.trim();
    }

    return Object.keys(preferences).length > 0 ? preferences as IUserPreferences : undefined;
  }, [state]);

  return {
    state,
    updateState,
    buildUserPreferences,
  };
};
