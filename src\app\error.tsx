"use client";

import { useEffect } from 'react';
import { logServerError } from '@/lib/error-utils';

const isDevelopment = process.env.NODE_ENV === 'development';

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  useEffect(() => {
    logServerError(error, 'Error boundary caught');
  }, [error]);

  return (
    <div className="min-h-screen bg-gray-100 flex flex-col items-center justify-center p-4">
      <div className="bg-white p-8 rounded-lg shadow-md w-full max-w-2xl">
        <h2 className="text-2xl font-bold text-red-600 mb-4">Something went wrong!</h2>
        <p className="mb-6">There was an error processing your request. Please try again.</p>
        
        <button
          onClick={() => reset()}
          className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors"
        >
          Try again
        </button>

        <details className="mt-6 border rounded p-4">
          <summary className="font-medium cursor-pointer text-gray-700">Error Details</summary>
          <div className="mt-2 p-3 bg-gray-50 rounded font-mono text-sm overflow-x-auto">
            {isDevelopment ? (
              <>
                <p className="font-semibold">Message:</p>
                <p className="mb-4 text-red-600">{error.message}</p>
                
                {error.digest && (
                  <>
                    <p className="font-semibold">Digest:</p>
                    <p className="text-gray-600">{error.digest}</p>
                  </>
                )}
              </>
            ) : (
              <p className="text-gray-600">
                An unexpected error occurred. Please try again or contact support if the problem persists.
                {error.digest && ` (Reference: ${error.digest})`}
              </p>
            )}
          </div>
        </details>
      </div>
    </div>
  );
}
