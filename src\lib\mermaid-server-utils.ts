// src/lib/mermaid-server-utils.ts
'use server';

import { exec } from 'child_process';
import fs from 'fs/promises';
import path from 'path';
import os from 'os';

/**
 * Converts a Mermaid script string to an SVG data URI.
 * @param mermaidScript The Mermaid script to convert.
 * @returns A promise that resolves to an SVG data URI string.
 */
export async function convertMermaidToSvgDataUri(mermaidScript: string): Promise<string> {
  const tempId = `mermaid-${Date.now()}-${Math.random().toString(36).substring(7)}`;
  const tempInputFile = path.join(os.tmpdir(), `${tempId}.mmd`);
  const tempOutputFile = path.join(os.tmpdir(), `${tempId}.svg`);

  try {
    await fs.writeFile(tempInputFile, mermaidScript, 'utf-8');

    // Ensure mmdc is executable. npx handles finding it in node_modules/.bin
    // Added --configFile to point to a minimal valid JSON to avoid potential default config issues
    // Added --outputFormat svg explicitly
    const mmdcCommand = `npx mmdc -i "${tempInputFile}" -o "${tempOutputFile}" --backgroundColor transparent --outputFormat svg`;
    
    await new Promise<void>((resolve, reject) => {
      exec(mmdcCommand, (error, stdout, stderr) => {
        if (error) {
          console.error(`Mermaid CLI Execution Error: ${error.message}`);
          console.error(`Mermaid CLI Stderr: ${stderr}`);
          console.error(`Mermaid CLI Stdout: ${stdout}`);
          reject(new Error(`Failed to convert Mermaid to SVG: ${stderr || error.message}`));
          return;
        }
        if (stderr) {
          // Mermaid CLI sometimes outputs warnings to stderr even on success
          console.warn(`Mermaid CLI Stderr (Warnings): ${stderr}`);
        }
        resolve();
      });
    });

    const svgContent = await fs.readFile(tempOutputFile, 'utf-8');
    const base64Svg = Buffer.from(svgContent).toString('base64');
    return `data:image/svg+xml;base64,${base64Svg}`;

  } catch (error) {
    console.error('Error in convertMermaidToSvgDataUri:', error);
    // Provide more context if it's a file system error
    if (error instanceof Error && 'path' in error) {
        throw new Error(`File system error during Mermaid conversion: ${error.message} on path ${error['path']}`);
    }
    throw error; // Re-throw the error to be handled by the caller
  } finally {
    // Clean up temporary files
    await fs.unlink(tempInputFile).catch(e => console.warn(`Failed to delete temp input file ${tempInputFile}: ${e.message}`));
    await fs.unlink(tempOutputFile).catch(e => console.warn(`Failed to delete temp output file ${tempOutputFile}: ${e.message}`));
  }
}
