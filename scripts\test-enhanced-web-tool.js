/**
 * Test the enhanced web research tool output format
 * Run with: node scripts/test-enhanced-web-tool.js
 */

require('dotenv').config();

async function testEnhancedWebTool() {
  console.log('🔧 Testing Enhanced Web Research Tool Output');
  console.log('============================================\n');

  // Simulate the enhanced web research tool behavior
  const query = 'serverless architecture best practices';
  console.log(`🔍 Testing Query: "${query}"`);

  try {
    // Simulate what the enhanced web research tool would return
    const toolOutput = await simulateEnhancedWebResearchTool(query);
    
    console.log('\n✅ ENHANCED WEB RESEARCH TOOL OUTPUT');
    console.log('===================================');
    
    console.log(`📊 Findings: ${toolOutput.findings.length}`);
    console.log(`🔗 Sources: ${toolOutput.sources.join(', ')}`);
    console.log(`📈 Total Results: ${toolOutput.totalResults}`);
    
    if (toolOutput.knowledgeGraph) {
      console.log(`📚 Knowledge Graph: Available`);
      console.log(`   ${toolOutput.knowledgeGraph.substring(0, 100)}...`);
    }

    console.log('\n🔍 SAMPLE FINDINGS:');
    console.log('------------------');
    toolOutput.findings.forEach((finding, index) => {
      console.log(`${index + 1}. ${finding.substring(0, 120)}...`);
    });

    console.log('\n🎯 TOOL OUTPUT VALIDATION');
    console.log('=========================');
    
    // Validate the output structure matches the expected schema
    const isValid = validateToolOutput(toolOutput);
    console.log(`✅ Schema Valid: ${isValid ? 'Yes' : 'No'}`);
    
    if (isValid) {
      console.log('✅ All required fields present');
      console.log('✅ Findings array populated');
      console.log('✅ Sources array populated');
      console.log('✅ Total results is a number');
      console.log('✅ Knowledge graph is optional string');
    }

    console.log('\n💡 INTEGRATION BENEFITS');
    console.log('======================');
    console.log('• Enhanced findings with source icons (🔍 🦁 📚)');
    console.log('• Multiple search sources for comprehensive coverage');
    console.log('• Knowledge graph integration for authoritative info');
    console.log('• Metadata about search quality and coverage');
    console.log('• Intelligent fallbacks for API failures');

    console.log('\n🚀 PROPOSAL GENERATION IMPACT');
    console.log('=============================');
    console.log('• AI receives richer context from multiple sources');
    console.log('• Knowledge graphs provide authoritative information');
    console.log('• Source diversity ensures comprehensive coverage');
    console.log('• Fallback mechanisms ensure uninterrupted service');
    console.log('• Enhanced metadata helps AI understand result quality');

    console.log('\n🎉 Enhanced Web Research Tool test completed successfully!');

  } catch (error) {
    console.error('❌ Enhanced web research tool test failed:', error.message);
  }
}

async function simulateEnhancedWebResearchTool(query) {
  const findings = [];
  let sources = [];
  let knowledgeGraph;
  let totalResults = 0;

  // Check API availability
  const hasSerper = !!process.env.SERPER_API_KEY;
  const hasBrave = !!process.env.BRAVE_API_KEY;

  if (!hasSerper && !hasBrave) {
    // Fallback simulation
    findings.push(
      `Simulated Finding: For '${query}', current best practices emphasize robust design, user-centric features, and leveraging modern technology stacks for optimal performance and maintainability.`
    );
    return {
      findings,
      sources: ['simulated'],
      totalResults: 1,
      knowledgeGraph: undefined
    };
  }

  // Simulate Serper search
  if (hasSerper) {
    try {
      const response = await fetch('https://google.serper.dev/search', {
        method: 'POST',
        headers: {
          'X-API-KEY': process.env.SERPER_API_KEY,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          q: query,
          num: 5,
          gl: 'us',
          hl: 'en',
        }),
      });

      if (response.ok) {
        const data = await response.json();
        sources.push('serper');

        // Add knowledge graph if available
        if (data.knowledgeGraph) {
          knowledgeGraph = `${data.knowledgeGraph.title}: ${data.knowledgeGraph.description}`;
          findings.push(`📚 Knowledge Graph: ${knowledgeGraph}`);
        }

        // Add organic results
        if (data.organic) {
          data.organic.slice(0, 3).forEach(result => {
            findings.push(`🔍 Finding from "${result.title}": ${result.snippet}`);
          });
          totalResults += data.organic.length;
        }
      }
    } catch (error) {
      console.warn('Serper simulation failed:', error.message);
    }
  }

  // Simulate Brave search
  if (hasBrave) {
    try {
      const params = new URLSearchParams({
        q: query,
        count: '3',
        offset: '0',
        mkt: 'en-US',
        safesearch: 'moderate',
      });

      const response = await fetch(`https://api.search.brave.com/res/v1/web/search?${params}`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'X-Subscription-Token': process.env.BRAVE_API_KEY,
        },
      });

      if (response.ok) {
        const data = await response.json();
        sources.push('brave');

        if (data.web?.results) {
          data.web.results.slice(0, 2).forEach(result => {
            findings.push(`🦁 Finding from "${result.title}": ${result.description}`);
          });
          totalResults += data.web.results.length;
        }
      }
    } catch (error) {
      console.warn('Brave simulation failed:', error.message);
    }
  }

  // Fallback if no results
  if (findings.length === 0) {
    findings.push(
      `No specific web results found for '${query}'. Consider refining the search query or checking for current trends in this area.`
    );
    totalResults = 0;
  }

  return {
    findings,
    sources: sources.length > 0 ? sources : ['fallback'],
    totalResults,
    knowledgeGraph
  };
}

function validateToolOutput(output) {
  // Validate the output matches the expected schema
  if (!output || typeof output !== 'object') return false;
  
  // Check required fields
  if (!Array.isArray(output.findings)) return false;
  if (!Array.isArray(output.sources)) return false;
  if (typeof output.totalResults !== 'number') return false;
  
  // Check optional fields
  if (output.knowledgeGraph !== undefined && typeof output.knowledgeGraph !== 'string') {
    return false;
  }
  
  return true;
}

// Run the test
testEnhancedWebTool().catch(console.error);
