
'use server';
/**
 * @fileOverview Generates Mermaid.js scripts for architecture diagrams.
 *
 * - generateArchitectureDiagram - A function that generates a Mermaid script based on a description and diagram type.
 * - GenerateArchitectureDiagramInput - The input type for the generateArchitectureDiagram function.
 * - GenerateArchitectureDiagramOutput - The return type for the generateArchitectureDiagram function.
 */

import {ai} from '@/ai/genkit';
import {z} from 'genkit';

const GenerateArchitectureDiagramInputSchema = z.object({
  architectureDescription: z
    .string()
    .describe('A description of the system or solution for which to generate an architecture diagram.'),
  diagramType: z.enum(['conceptual', 'reference'])
    .describe('The type of architecture diagram to generate: "conceptual" or "reference".'),
});
export type GenerateArchitectureDiagramInput = z.infer<typeof GenerateArchitectureDiagramInputSchema>;

const GenerateArchitectureDiagramOutputSchema = z.object({
  mermaidScript: z.string().describe('The generated Mermaid.js script for the architecture diagram.'),
});
export type GenerateArchitectureDiagramOutput = z.infer<typeof GenerateArchitectureDiagramOutputSchema>;

export async function generateArchitectureDiagram(input: GenerateArchitectureDiagramInput): Promise<GenerateArchitectureDiagramOutput> {
  return generateArchitectureDiagramFlow(input);
}

const architectureVisualizerPrompt = ai.definePrompt({
  name: 'architectureVisualizerPrompt',
  input: {schema: GenerateArchitectureDiagramInputSchema},
  output: {schema: GenerateArchitectureDiagramOutputSchema},
  prompt: `You are an expert system architect and AI assistant specialized in creating architecture diagrams using Mermaid.js syntax.
Your task is to generate a Mermaid.js script based on the provided architecture description and the desired diagram type.

Architecture Description:
{{{architectureDescription}}}

Diagram Type: {{{diagramType}}}

Instructions for Diagram Types:
- Conceptual Diagram: Focus on the "what." Show the overall system structure, key concepts, main components, and their high-level relationships. Avoid excessive detail about specific technologies or implementation specifics unless critical to the concept. Use clear, high-level labels.
- Reference Architecture Diagram: Focus on "how" components interact. Show key components, their connections, data flows, and dependencies. This might include specific technologies or services if relevant to the architecture pattern being depicted. Ensure clarity in interactions and interfaces.

Generate ONLY the Mermaid.js script. Do not include any explanations, titles, or markdown formatting like \`\`\`mermaid ... \`\`\`. The output should be directly usable as a Mermaid script.
Start the script with 'graph TD;' or 'graph LR;' as appropriate.
Ensure the diagram is reasonably complex but clear and readable.
`,
  config: {
    safetySettings: [
      {
        category: 'HARM_CATEGORY_DANGEROUS_CONTENT',
        threshold: 'BLOCK_ONLY_HIGH',
      },
      {
        category: 'HARM_CATEGORY_HARASSMENT',
        threshold: 'BLOCK_MEDIUM_AND_ABOVE',
      },
      {
        category: 'HARM_CATEGORY_HATE_SPEECH',
        threshold: 'BLOCK_MEDIUM_AND_ABOVE',
      },
    ],
  },
});

const generateArchitectureDiagramFlow = ai.defineFlow(
  {
    name: 'generateArchitectureDiagramFlow',
    inputSchema: GenerateArchitectureDiagramInputSchema,
    outputSchema: GenerateArchitectureDiagramOutputSchema,
  },
  async (input) => {
    const {output} = await architectureVisualizerPrompt(input);
    
    if (!output?.mermaidScript) {
        throw new Error('AI failed to generate a Mermaid script.');
    }
    
    // Attempt to "fix" common issues like markdown backticks and extra whitespace
    let script = output.mermaidScript.trim();
    
    if (script.startsWith('```mermaid')) {
        script = script.substring('```mermaid'.length);
    }
    // Handle cases where only ``` is used without 'mermaid'
    if (script.startsWith('```')) {
        script = script.substring('```'.length);
    }
    if (script.endsWith('```')) {
        script = script.substring(0, script.length - '```'.length);
    }
    
    // Final trim after potential removals
    script = script.trim();

    if (!script) { // Check if the script became empty after cleaning
        throw new Error('AI generated an empty or invalid Mermaid script after cleanup.');
    }
        
    return { mermaidScript: script };
  }
);

