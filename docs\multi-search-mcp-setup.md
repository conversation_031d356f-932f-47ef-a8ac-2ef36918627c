# Multi-Search MCP Setup Guide

This guide shows you how to set up multiple web search MCPs for comprehensive research capabilities.

## Available Web Search MCPs

### Tier 1 (Essential - Recommended)

#### 1. Serper Search MCP (Premium Quality)
- **Repository**: `nighttrek/serper-search-mcp`
- **Features**: 
  - Google search via Serper API
  - Deep research with iterative search
  - Knowledge graph integration
  - "People also ask" questions
  - Related searches
  - Advanced quality metrics
- **API Key Required**: Yes (Serper API - $5/1000 searches)
- **Trust Score**: 9.1/10

#### 2. DuckDuckGo MCP (Privacy-Focused)
- **Repository**: `nickclyde/duckduckgo-mcp-server`
- **Features**: 
  - Privacy-focused web search
  - No tracking
  - No API key required
- **API Key Required**: No
- **Cost**: Free

#### 3. Free Web Search MCP (Backup)
- **Repository**: `pskill9/web-search`
- **Features**: 
  - Free Google search (no API keys required)
  - Simple implementation
  - Basic search results
- **API Key Required**: No
- **Trust Score**: 4.7/10

### Tier 2 (Enhanced Coverage)

#### 4. Brave Search MCP
- **Repository**: `leehanchung/bing-search-mcp` (from awesome-mcp-servers)
- **Features**: Web search using Brave Search API
- **API Key Required**: Yes (Brave Search API)
- **Privacy-focused**: Yes

#### 5. Bing Search MCP
- **Repository**: `leehanchung/bing-search-mcp`
- **Features**: Web search using Microsoft Bing Search API
- **API Key Required**: Yes (Bing Search API)

#### 6. Linkup Search MCP
- **Provider**: Linkup Technologies
- **Features**: Real-time web search for information gathering and fact-checking
- **API Key Required**: Yes (Linkup API)

## Installation Instructions

### 1. Serper Search MCP

```bash
# Clone and build
git clone https://github.com/nighttrek/serper-search-mcp.git
cd serper-search-mcp
pnpm install
pnpm run build
```

Create `.env` file:
```env
SERPER_API_KEY=your_serper_api_key_here
```

### 2. DuckDuckGo MCP

```bash
# Install via npm
npm install -g duckduckgo-mcp-server
```

### 3. Free Web Search MCP

```bash
# Clone and build
git clone https://github.com/pskill9/web-search.git
cd web-search
npm install
npm run build
```

## Claude Desktop Configuration

Add to your `claude_desktop_config.json`:

```json
{
  "mcpServers": {
    "serper-search": {
      "command": "node",
      "args": ["/path/to/serper-search-mcp/build/index.js"],
      "env": {
        "SERPER_API_KEY": "your_serper_api_key_here"
      }
    },
    "duckduckgo-search": {
      "command": "duckduckgo-mcp-server"
    },
    "free-web-search": {
      "command": "node",
      "args": ["/path/to/web-search/build/index.js"]
    },
    "brave-search": {
      "command": "node",
      "args": ["/path/to/brave-search-mcp/build/index.js"],
      "env": {
        "BRAVE_API_KEY": "your_brave_api_key_here"
      }
    },
    "bing-search": {
      "command": "node",
      "args": ["/path/to/bing-search-mcp/build/index.js"],
      "env": {
        "BING_API_KEY": "your_bing_api_key_here"
      }
    }
  }
}
```

## Research Strategy

### Multi-Search Approach

1. **Primary Search**: Use Serper Search MCP for high-quality, comprehensive results
2. **Privacy Search**: Use DuckDuckGo MCP for privacy-focused queries
3. **Backup Search**: Use Free Web Search MCP if API limits are reached
4. **Cross-Validation**: Use Brave/Bing for different perspectives
5. **Consolidation**: Combine results from multiple sources

### Example Research Workflow

```typescript
// Example multi-search research function
async function comprehensiveResearch(query: string) {
  const results = [];
  
  // Primary search with Serper (high quality)
  try {
    const serperResults = await searchWithSerper(query);
    results.push({ source: 'serper', data: serperResults });
  } catch (error) {
    console.log('Serper search failed, using backup');
  }
  
  // Privacy-focused search with DuckDuckGo
  const duckResults = await searchWithDuckDuckGo(query);
  results.push({ source: 'duckduckgo', data: duckResults });
  
  // Alternative perspective with Brave
  const braveResults = await searchWithBrave(query);
  results.push({ source: 'brave', data: braveResults });
  
  // Consolidate and analyze results
  return consolidateResults(results);
}
```

## API Key Setup

### Serper API
1. Visit [serper.dev](https://serper.dev)
2. Sign up for an account
3. Get your API key
4. Pricing: $5 per 1,000 searches

### Brave Search API
1. Visit [brave.com/search/api](https://brave.com/search/api)
2. Apply for API access
3. Get your API key
4. Check current pricing

### Bing Search API
1. Visit [Azure Cognitive Services](https://azure.microsoft.com/en-us/services/cognitive-services/bing-web-search-api/)
2. Create a Bing Search resource
3. Get your API key
4. Pay-per-use pricing

## Benefits of Multi-Search Setup

1. **Redundancy**: If one service fails, others continue working
2. **Diverse Results**: Different search engines may return different perspectives
3. **Cost Management**: Use free services when possible, premium when needed
4. **Privacy Options**: DuckDuckGo for privacy-sensitive queries
5. **Quality Assurance**: Cross-reference results across multiple sources
6. **Rate Limit Management**: Distribute queries across multiple services

## Troubleshooting

### Common Issues

1. **API Key Errors**: Ensure all API keys are correctly set in environment variables
2. **Rate Limits**: Implement proper rate limiting and fallback mechanisms
3. **Network Issues**: Add retry logic for failed requests
4. **Result Quality**: Use Serper for best quality, others for backup/diversity

### Debugging

```bash
# Check MCP server logs
tail -f ~/.config/Claude/logs/mcp*.log

# Test individual MCPs
npx @modelcontextprotocol/inspector <mcp-command>
```

## Next Steps

1. Set up the essential Tier 1 MCPs first
2. Test each MCP individually
3. Implement multi-search logic in your application
4. Add result consolidation and deduplication
5. Monitor usage and costs
6. Expand to Tier 2 MCPs as needed
