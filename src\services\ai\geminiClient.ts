// src/services/ai/geminiClient.ts

interface IGeminiTextGenerationRequest {
  prompt: string;
  temperature?: number;
  maxOutputTokens?: number;
  topP?: number;
  topK?: number;
}

interface IPart {
  text: string;
}

interface IContent {
  role?: string;
  parts: IPart[];
}

interface ICandidate {
  content: IContent;
  finishReason: string;
  index: number;
  safetyRatings: Array<{
    category: string;
    probability: string;
  }>;
}

interface IGeminiTextGenerationResponse {
  candidates: ICandidate[];
  promptFeedback?: {
    safetyRatings: Array<{
      category: string;
      probability: string;
    }>;
  };
}

class GeminiClient {
  private apiKey: string;

  constructor(apiKey: string | undefined) {
    if (!apiKey) {
      console.warn('Gemini API key is not set. Using mock responses.');
      this.apiKey = 'MOCK_API_KEY'; // Use a mock key if undefined
    } else {
      this.apiKey = apiKey;
    }
  }

  /**
   * Simulates generating text using the Gemini API.
   * In a real implementation, this would make an HTTP request to the Gemini API.
   * @param params - The parameters for text generation.
   * @returns A Promise resolving to the simulated generated text.
   */
  /**
   * Generates text using the Gemini API
   * @param params - The parameters for text generation including the prompt and optional parameters
   * @returns A Promise resolving to the generated text from Gemini
   * @throws {Error} If the API request fails or returns an error
   */
  public async generateText(params: IGeminiTextGenerationRequest): Promise<string> {
    const { prompt, temperature = 0.7, maxOutputTokens = 2048, topP = 0.8, topK = 40 } = params;
    
    // Log the request (truncated for logging)
    const truncatedPrompt = prompt.length > 100 ? `${prompt.substring(0, 100)}...` : prompt;
    console.log(`[GeminiClient] Generating text for prompt: "${truncatedPrompt}"`);

    // Use mock response if no API key is set
    if (this.apiKey === 'MOCK_API_KEY' || !process.env.GEMINI_API_KEY) {
      console.warn('Using mock response as no valid Gemini API key was provided');
      await new Promise(resolve => setTimeout(resolve, 500)); // Simulate network latency
      return `[MOCK] Gemini response for: ${truncatedPrompt}`;
    }

    try {
      const modelName = 'gemini-pro';
      const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/${modelName}:generateContent?key=${this.apiKey}`;

      const requestBody = {
        contents: [{
          parts: [{
            text: prompt
          }]
        }],
        generationConfig: {
          temperature,
          maxOutputTokens,
          topP,
          topK,
        }
      };

      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          `Gemini API request failed with status ${response.status}: ${JSON.stringify(errorData)}`
        );
      }

      const data: IGeminiTextGenerationResponse = await response.json();

      // Extract the generated text from the response
      if (!data.candidates || data.candidates.length === 0) {
        throw new Error('No candidates in the response from Gemini API');
      }

      const generatedText = data.candidates[0]?.content?.parts[0]?.text;
      
      if (!generatedText) {
        console.error('Unexpected response format from Gemini API:', data);
        throw new Error('Could not extract generated text from the API response');
      }

      return generatedText;

    } catch (error) {
      console.error('Error calling Gemini API:', error);
      throw new Error(`Failed to generate text with Gemini: ${error instanceof Error ? error.message : String(error)}`);
    }
  }
}

// Initialize and export a singleton instance of the client
// The API key would typically come from environment variables
const geminiClient = new GeminiClient(process.env.GEMINI_API_KEY);

export default geminiClient;
