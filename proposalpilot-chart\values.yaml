# Default values for proposalpilot
replicaCount: 1

image:
  repository: proposalpilot
  pullPolicy: IfNotPresent
  tag: "latest"

imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

serviceAccount:
  create: false

podAnnotations: {}

podSecurityContext: {}

securityContext: {}

service:
  type: ClusterIP
  port: 3000

# PostgreSQL Configuration
postgresql:
  enabled: true
  global:
    postgresql:
      auth:
        database: proposalpilot
        username: proposalpilot
  auth:
    postgresPassword: ""  # Will be set via secrets
    password: ""  # Will be set via secrets
    database: proposalpilot
    username: proposalpilot
  primary:
    persistence:
      enabled: true
      size: 8Gi
      storageClass: ""  # Use default storage class
    service:
      ports:
        postgresql: 5433
    resources:
      requests:
        cpu: 100m
        memory: 256Mi
      limits:
        cpu: 500m
        memory: 1Gi
  readReplicas:
    replicaCount: 0  # Disable read replicas for now
  metrics:
    enabled: false  # Disable metrics by default

# Application Secrets - These should be provided via --set or a values file
secrets:
  # API Keys
  geminiApiKey: ""
  braveApiKey: ""
  serperApiKey: ""
  # Database
  postgresPassword: ""  # Will be used for the 'proposalpilot' user
  postgresPostgresPassword: ""  # Will be used for the 'postgres' admin user

ingress:
  enabled: false
  className: ""
  annotations: {}
  hosts:
    - host: proposalpilot.local
      paths:
        - path: /
          pathType: ImplementationSpecific
  tls: []

resources: 
  limits:
    cpu: "1"
    memory: "1Gi"
  requests:
    cpu: "100m"
    memory: "128Mi"

autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 3
  targetCPUUtilizationPercentage: 80

nodeSelector: {}

tolerations: []

affinity: {}
