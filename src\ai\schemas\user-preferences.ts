import { z } from 'zod';

export const UserPreferencesSchema = z.object({
  preferredTechnologies: z.array(z.string()).optional().describe('Preferred technologies, platforms, or tools (e.g., Low-code Platforms, Specific Cloud Providers, Key Frameworks)'),
  technicalConstraints: z.array(z.string()).optional().describe('Technical constraints or limitations to consider'),
  budgetConsiderations: z.string().optional().describe('Budget constraints or cost considerations'),
  timelineConstraints: z.string().optional().describe('Timeline or delivery constraints'),
  complianceRequirements: z.array(z.string()).optional().describe('Specific compliance or regulatory requirements'),
  integrationRequirements: z.array(z.string()).optional().describe('Existing systems that must be integrated with'),
  scalabilityRequirements: z.string().optional().describe('Specific scalability or performance requirements'),
  securityRequirements: z.array(z.string()).optional().describe('Specific security requirements or standards'),
  additionalConsiderations: z.string().optional().describe('Any other considerations or preferences'),
}).describe('User preferences and considerations to guide proposal generation');

export type UserPreferences = z.infer<typeof UserPreferencesSchema>;
