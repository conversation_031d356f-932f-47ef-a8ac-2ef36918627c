'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Loader2, Search, ExternalLink, Clock, Database } from 'lucide-react';
import { performResearchAction } from '@/app/actions';
import type { SearchSummary } from '@/lib/unified-search-engine';

interface ResearchDisplayProps {
  initialQuery?: string;
  onResearchComplete?: (research: SearchSummary) => void;
}

export function ResearchDisplay({ initialQuery = '', onResearchComplete }: ResearchDisplayProps) {
  const [query, setQuery] = useState(initialQuery);
  const [research, setResearch] = useState<SearchSummary | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleResearch = async () => {
    if (!query.trim()) return;

    setIsLoading(true);
    setError(null);

    try {
      const result = await performResearchAction(query.trim());
      setResearch(result);
      onResearchComplete?.(result);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Research failed');
    } finally {
      setIsLoading(false);
    }
  };

  const getSourceIcon = (source: string) => {
    switch (source) {
      case 'serper':
        return '🔍';
      case 'brave-api':
        return '🦁';
      case 'brave-mcp':
        return '🛡️';
      case 'serper-kg':
        return '📚';
      case 'duckduckgo':
        return '🦆';
      case 'fallback':
        return '⚠️';
      default:
        return '🔗';
    }
  };

  const getSourceName = (source: string) => {
    switch (source) {
      case 'serper':
        return 'Google (Serper)';
      case 'brave-api':
        return 'Brave Search API';
      case 'brave-mcp':
        return 'Brave Search MCP';
      case 'serper-kg':
        return 'Knowledge Graph';
      case 'duckduckgo':
        return 'DuckDuckGo';
      case 'fallback':
        return 'Fallback';
      default:
        return source;
    }
  };

  return (
    <div className="space-y-6">
      {/* Research Input */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Search className="h-5 w-5" />
            Web Research
          </CardTitle>
          <CardDescription>
            Search across multiple sources for comprehensive research
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex gap-2">
            <Input
              placeholder="Enter your research query..."
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              onKeyDown={(e) => e.key === 'Enter' && !isLoading && handleResearch()}
              disabled={isLoading}
            />
            <Button
              onClick={handleResearch}
              disabled={isLoading || !query.trim()}
              className="min-w-[100px]"
            >
              {isLoading ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  Searching...
                </>
              ) : (
                <>
                  <Search className="h-4 w-4 mr-2" />
                  Research
                </>
              )}
            </Button>
          </div>
          {error && (
            <p className="text-sm text-red-600 mt-2">{error}</p>
          )}
        </CardContent>
      </Card>

      {/* Research Results */}
      {research && (
        <div className="space-y-4">
          {/* Research Summary */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>Research Summary</span>
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <Clock className="h-4 w-4" />
                  {research.searchTime}ms
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">{research.uniqueUrls}</div>
                  <div className="text-sm text-muted-foreground">Unique Results</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">{research.sourcesUsed.length}</div>
                  <div className="text-sm text-muted-foreground">Sources Used</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-600">{research.totalSources}</div>
                  <div className="text-sm text-muted-foreground">Total Results</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-orange-600">
                    {research.duplicatesRemoved || (research.totalSources - research.uniqueUrls)}
                  </div>
                  <div className="text-sm text-muted-foreground">Duplicates Removed</div>
                </div>
              </div>

              <div className="flex flex-wrap gap-2">
                {research.sourcesUsed.map((source) => (
                  <Badge key={source} variant="secondary" className="flex items-center gap-1">
                    <span>{getSourceIcon(source)}</span>
                    {getSourceName(source)}
                  </Badge>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Knowledge Graph */}
          {research.knowledgeGraph && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Database className="h-5 w-5" />
                  Knowledge Graph
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="bg-blue-50 dark:bg-blue-950 p-4 rounded-lg">
                  <h4 className="font-semibold text-blue-900 dark:text-blue-100 mb-2">
                    {research.knowledgeGraph.title}
                  </h4>
                  <p className="text-blue-800 dark:text-blue-200 mb-3">
                    {research.knowledgeGraph.description}
                  </p>
                  {research.knowledgeGraph.website && (
                    <a
                      href={research.knowledgeGraph.website}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="inline-flex items-center gap-1 text-blue-600 hover:text-blue-800 text-sm"
                    >
                      <ExternalLink className="h-3 w-3" />
                      Official Website
                    </a>
                  )}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Search Results */}
          <Card>
            <CardHeader>
              <CardTitle>Search Results</CardTitle>
              <CardDescription>
                {research.results.length} results from comprehensive web search
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {research.results.map((result, index) => (
                  <div
                    key={`${result.url}-${index}`}
                    className="border rounded-lg p-4 hover:bg-muted/50 transition-colors"
                  >
                    <div className="flex items-start justify-between gap-4">
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-2">
                          <span className="text-lg">{getSourceIcon(result.source)}</span>
                          <h4 className="font-semibold text-sm text-primary hover:underline">
                            <a
                              href={result.url}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="line-clamp-2"
                            >
                              {result.title}
                            </a>
                          </h4>
                        </div>
                        <p className="text-sm text-muted-foreground mb-2 line-clamp-3">
                          {result.snippet}
                        </p>
                        <div className="flex items-center gap-2">
                          <Badge variant="outline" className="text-xs">
                            {getSourceName(result.source)}
                          </Badge>
                          <span className="text-xs text-muted-foreground">
                            Relevance: {(result.relevanceScore * 100).toFixed(0)}%
                          </span>
                        </div>
                      </div>
                      <a
                        href={result.url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="flex-shrink-0 p-2 hover:bg-muted rounded-md transition-colors"
                      >
                        <ExternalLink className="h-4 w-4" />
                      </a>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}

export default ResearchDisplay;
