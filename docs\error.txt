docker build -t proposalpilot .
[+] Building 35.1s (11/18)                                                                                               docker:desktop-linux
 => [internal] load build definition from Dockerfile                                                                                     0.0s
 => => transferring dockerfile: 926B                                                                                                     0.0s
 => [internal] load metadata for docker.io/library/node:20-alpine                                                                        1.7s
 => [internal] load .dockerignore                                                                                                        0.0s
 => => transferring context: 132B                                                                                                        0.0s
 => [builder 1/7] FROM docker.io/library/node:20-alpine@sha256:d3507a213936fe4ef54760a186e113db5188472d9efdf491686bd94580a1c1e8          0.0s
 => [internal] load build context                                                                                                        0.0s
 => => transferring context: 44.61kB                                                                                                     0.0s
 => CACHED [builder 2/7] WORKDIR /app                                                                                                    0.0s
 => CACHED [builder 3/7] COPY package*.json ./                                                                                           0.0s
 => CACHED [builder 4/7] COPY package-lock.json* ./                                                                                      0.0s
 => CACHED [builder 5/7] RUN npm ci                                                                                                      0.0s
 => [builder 6/7] COPY . .                                                                                                               0.1s
 => ERROR [builder 7/7] RUN npm run build                                                                                               33.2s
------
 > [builder 7/7] RUN npm run build:
0.307 
0.307 > nextn@0.1.0 build
0.307 > next build
0.307
0.695  ⚠ Invalid next.config.mjs options detected:
0.696  ⚠     Unrecognized key(s) in object: 'allowedDevOrigins' at "experimental"
0.696  ⚠ See more info here: https://nextjs.org/docs/messages/invalid-next-config
0.714 Attention: Next.js now collects completely anonymous telemetry regarding usage.
0.714 This information is used to shape Next.js' roadmap and prioritize features.
0.714 You can learn more, including how to opt-out if you'd not like to participate in this anonymous program, by visiting the following URL: 
0.714 https://nextjs.org/telemetry
0.714
0.753    ▲ Next.js 15.3.2
0.753    - Environments: .env
0.753
0.802    Creating an optimized production build ...
19.42  ⚠ Compiled with warnings in 18.0s
19.42
19.42 ./node_modules/@opentelemetry/instrumentation/build/esm/platform/node/instrumentation.js
19.42 Critical dependency: the request of a dependency is an expression
19.42
19.42 Import trace for requested module:
19.42 ./node_modules/@opentelemetry/instrumentation/build/esm/platform/node/instrumentation.js
19.42 ./node_modules/@opentelemetry/instrumentation/build/esm/platform/node/index.js
19.42 ./node_modules/@opentelemetry/instrumentation/build/esm/platform/index.js
19.42 ./node_modules/@opentelemetry/instrumentation/build/esm/index.js
19.42 ./node_modules/@opentelemetry/sdk-node/build/src/sdk.js
19.42 ./node_modules/@opentelemetry/sdk-node/build/src/index.js
19.42 ./node_modules/@genkit-ai/core/lib/tracing.js
19.42 ./node_modules/genkit/lib/tracing.js
19.42 ./node_modules/@genkit-ai/googleai/lib/gemini.js
19.42 ./node_modules/@genkit-ai/googleai/lib/index.mjs
19.42 ./src/ai/genkit.ts
19.42 ./src/ai/flows/amendProposalFlow.ts
19.42 ./src/app/api/proposal/amend/route.ts
19.42
19.42 ./node_modules/@opentelemetry/sdk-node/build/src/TracerProviderWithEnvExporter.js
19.42 Module not found: Can't resolve '@opentelemetry/exporter-jaeger' in '/app/node_modules/@opentelemetry/sdk-node/build/src'
19.42
19.42 Import trace for requested module:
19.42 ./node_modules/@opentelemetry/sdk-node/build/src/TracerProviderWithEnvExporter.js
19.42 ./node_modules/@opentelemetry/sdk-node/build/src/sdk.js
19.42 ./node_modules/@opentelemetry/sdk-node/build/src/index.js
19.42 ./node_modules/@genkit-ai/core/lib/tracing.js
19.42 ./node_modules/@genkit-ai/ai/lib/chat.js
19.42 ./node_modules/genkit/lib/common.js
19.42 ./node_modules/genkit/lib/index.mjs
19.42 ./src/ai/flows/proposal-enhancer.ts
19.42 ./src/app/actions.ts
19.42
19.42 ./node_modules/@opentelemetry/sdk-node/build/src/TracerProviderWithEnvExporter.js
19.42 Module not found: Can't resolve '@opentelemetry/exporter-jaeger' in '/app/node_modules/@opentelemetry/sdk-node/build/src'
19.42
19.42 Import trace for requested module:
19.42 ./node_modules/@opentelemetry/sdk-node/build/src/TracerProviderWithEnvExporter.js
19.42 ./node_modules/@opentelemetry/sdk-node/build/src/sdk.js
19.42 ./node_modules/@opentelemetry/sdk-node/build/src/index.js
19.42 ./node_modules/@genkit-ai/core/lib/tracing.js
19.42 ./node_modules/genkit/lib/tracing.js
19.42 ./node_modules/@genkit-ai/googleai/lib/gemini.js
19.42 ./node_modules/@genkit-ai/googleai/lib/index.mjs
19.42 ./src/ai/genkit.ts
19.42 ./src/ai/flows/amendProposalFlow.ts
19.42 ./src/app/api/proposal/amend/route.ts
19.42
19.42 ./node_modules/handlebars/lib/index.js
19.42 require.extensions is not supported by webpack. Use a loader instead.
19.42
19.42 Import trace for requested module:
19.42 ./node_modules/handlebars/lib/index.js
19.42 ./node_modules/dotprompt/dist/index.js
19.42 ./node_modules/@genkit-ai/core/lib/registry.js
19.42 ./node_modules/genkit/lib/registry.js
19.42 ./node_modules/genkit/lib/genkit.js
19.42 ./node_modules/genkit/lib/index.mjs
19.42 ./src/ai/flows/proposal-enhancer.ts
19.42 ./src/app/actions.ts
19.42
19.42 ./node_modules/handlebars/lib/index.js
19.42 require.extensions is not supported by webpack. Use a loader instead.
19.42
19.42 Import trace for requested module:
19.42 ./node_modules/handlebars/lib/index.js
19.42 ./node_modules/dotprompt/dist/index.js
19.42 ./node_modules/@genkit-ai/core/lib/registry.js
19.42 ./node_modules/genkit/lib/registry.js
19.42 ./node_modules/genkit/lib/genkit.js
19.42 ./node_modules/genkit/lib/index.mjs
19.42 ./src/ai/flows/proposal-enhancer.ts
19.42 ./src/app/actions.ts
19.42
19.42 ./node_modules/handlebars/lib/index.js
19.42 require.extensions is not supported by webpack. Use a loader instead.
19.42
19.42 Import trace for requested module:
19.42 ./node_modules/handlebars/lib/index.js
19.42 ./node_modules/dotprompt/dist/index.js
19.42 ./node_modules/@genkit-ai/core/lib/registry.js
19.42 ./node_modules/genkit/lib/registry.js
19.42 ./node_modules/genkit/lib/genkit.js
19.42 ./node_modules/genkit/lib/index.mjs
19.42 ./src/ai/flows/proposal-enhancer.ts
19.42 ./src/app/actions.ts
19.42
19.42 ./node_modules/handlebars/lib/index.js
19.42 require.extensions is not supported by webpack. Use a loader instead.
19.42
19.42 Import trace for requested module:
19.42 ./node_modules/handlebars/lib/index.js
19.42 ./node_modules/dotprompt/dist/index.js
19.42 ./node_modules/@genkit-ai/core/lib/registry.js
19.42 ./node_modules/genkit/lib/registry.js
19.42 ./node_modules/genkit/lib/genkit.js
19.42 ./node_modules/genkit/lib/index.mjs
19.42 ./src/ai/tools/web-researcher.ts
19.42 ./src/app/api/test-brave-search/route.ts
19.42
19.42 ./node_modules/handlebars/lib/index.js
19.42 require.extensions is not supported by webpack. Use a loader instead.
19.42
19.42 Import trace for requested module:
19.42 ./node_modules/handlebars/lib/index.js
19.42 ./node_modules/dotprompt/dist/index.js
19.42 ./node_modules/@genkit-ai/core/lib/registry.js
19.42 ./node_modules/genkit/lib/registry.js
19.42 ./node_modules/genkit/lib/genkit.js
19.42 ./node_modules/genkit/lib/index.mjs
19.42 ./src/ai/tools/web-researcher.ts
19.42 ./src/app/api/test-brave-search/route.ts
19.42
19.42 ./node_modules/handlebars/lib/index.js
19.42 require.extensions is not supported by webpack. Use a loader instead.
19.42
19.42 Import trace for requested module:
19.42 ./node_modules/handlebars/lib/index.js
19.42 ./node_modules/dotprompt/dist/index.js
19.42 ./node_modules/@genkit-ai/core/lib/registry.js
19.42 ./node_modules/genkit/lib/registry.js
19.42 ./node_modules/genkit/lib/genkit.js
19.42 ./node_modules/genkit/lib/index.mjs
19.42 ./src/ai/tools/web-researcher.ts
19.42 ./src/app/api/test-brave-search/route.ts
19.42
19.42 ./node_modules/@genkit-ai/flow/node_modules/@genkit-ai/core/lib/config.js
19.42 Critical dependency: require function is used in a way in which dependencies cannot be statically extracted
19.42
19.42 Import trace for requested module:
19.42 ./node_modules/@genkit-ai/flow/node_modules/@genkit-ai/core/lib/config.js
19.42 ./node_modules/@genkit-ai/flow/node_modules/@genkit-ai/core/lib/tracing.js
19.42 ./node_modules/@genkit-ai/flow/lib/flow.js
19.42 ./node_modules/@genkit-ai/flow/lib/index.mjs
19.42 ./src/app/api/proposal/amend/route.ts
19.42
19.42 ./node_modules/@genkit-ai/flow/node_modules/@genkit-ai/core/lib/tracing/localFileTraceStore.js
19.42 Critical dependency: require function is used in a way in which dependencies cannot be statically extracted
19.42
19.42 Import trace for requested module:
19.42 ./node_modules/@genkit-ai/flow/node_modules/@genkit-ai/core/lib/tracing/localFileTraceStore.js
19.42 ./node_modules/@genkit-ai/flow/node_modules/@genkit-ai/core/lib/tracing.js
19.42 ./node_modules/@genkit-ai/flow/lib/flow.js
19.42 ./node_modules/@genkit-ai/flow/lib/index.mjs
19.42 ./src/app/api/proposal/amend/route.ts
19.42
28.75  ✓ Compiled successfully in 25.0s
28.75    Linting and checking validity of types ...
32.96 Failed to compile.
32.96
32.96 ./src/lib/unified-search-engine.ts:710:31
32.96 Type error: Argument of type 'string | undefined' is not assignable to parameter of type 'string'.
32.96   Type 'undefined' is not assignable to type 'string'.
32.96
32.96   708 |     if (this.resultCache.size > 100) {
32.96   709 |       const oldestKey = this.resultCache.keys().next().value;
32.96 > 710 |       this.resultCache.delete(oldestKey);
32.96       |                               ^
32.96   711 |     }
32.96   712 |
32.96   713 |     this.resultCache.set(key, {
32.99 Next.js build worker exited with code: 1 and signal: null
------
Dockerfile:16
--------------------
  14 |
  15 |     # Build the application
  16 | >>> RUN npm run build
  17 |
  18 |     # Stage 2: Create the production image
--------------------
ERROR: failed to solve: process "/bin/sh -c npm run build" did not complete successfully: exit code: 1

View build details: docker-desktop://dashboard/build/desktop-linux/desktop-linux/ns9znztfg6b6nhza4x5ch9gz4