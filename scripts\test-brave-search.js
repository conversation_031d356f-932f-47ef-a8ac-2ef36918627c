#!/usr/bin/env node

/**
 * Test script for Brave Search MCP integration
 * 
 * This script tests the MCP client connection and performs a sample search
 * to verify that the integration is working correctly.
 */

const { performBraveWebSearch } = require('../src/lib/mcp-client.ts');
require('dotenv').config({ path: '.env.local' });

async function testBraveSearch() {
  console.log('🔍 Testing Brave Search MCP Integration...\n');

  // Check if API key is configured
  if (!process.env.BRAVE_API_KEY) {
    console.error('❌ BRAVE_API_KEY not found in environment variables');
    console.log('📝 Please add your Brave Search API key to .env.local');
    console.log('   Example: BRAVE_API_KEY=your_api_key_here');
    process.exit(1);
  }

  console.log('✅ API key found');
  console.log('🔗 Attempting to connect to Brave Search MCP server...\n');

  try {
    // Test search query
    const testQuery = 'cloud native architecture best practices 2024';
    console.log(`🔍 Searching for: "${testQuery}"`);
    
    const startTime = Date.now();
    const results = await performBraveWebSearch(testQuery, 3, 0);
    const endTime = Date.now();
    
    console.log(`⏱️  Search completed in ${endTime - startTime}ms\n`);
    
    if (results.results && results.results.length > 0) {
      console.log('✅ Search successful! Results:');
      console.log(`📊 Found ${results.results.length} results\n`);
      
      results.results.forEach((result, index) => {
        console.log(`${index + 1}. ${result.title}`);
        console.log(`   URL: ${result.url}`);
        console.log(`   Description: ${result.description.substring(0, 100)}...`);
        console.log('');
      });
      
      console.log('🎉 Brave Search MCP integration is working correctly!');
      console.log('💡 You can now use real web search in your ProposalPilot application.');
      
    } else {
      console.log('⚠️  Search completed but no results found');
      console.log('💡 This might be normal for very specific queries');
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.log('\n🔧 Troubleshooting tips:');
    console.log('1. Verify your API key is correct');
    console.log('2. Check your internet connection');
    console.log('3. Ensure you have remaining API quota');
    console.log('4. Try running: npm install @modelcontextprotocol/sdk');
    process.exit(1);
  }
}

// Run the test
testBraveSearch().catch(console.error);
