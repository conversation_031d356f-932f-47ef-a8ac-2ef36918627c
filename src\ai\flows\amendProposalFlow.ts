// src/ai/flows/amendProposalFlow.ts
import { defineFlow } from '@genkit-ai/flow';
import { ai } from '../genkit'; // Assuming ai is exported from genkit.ts and configured
import { amendRequestSchema, amendedProposalSchema } from '../schemas/amendmentSchemas';
import { z } from 'zod';
import type { ISuggestion } from '@/types/proposal';

export const amendProposalFlow = defineFlow(
  {
    name: 'amendProposalFlow',
    inputSchema: amendRequestSchema,
    outputSchema: amendedProposalSchema,
  },
  async (payload: z.infer<typeof amendRequestSchema>) => {
    const { originalProposal, feedback } = payload;

    // Use full suggestion objects if available, otherwise create them from text
    let selectedSuggestions: ISuggestion[] = [];
    
    if (feedback.selectedSuggestions && feedback.selectedSuggestions.length > 0) {
      // Use the full suggestion objects when provided
      selectedSuggestions = feedback.selectedSuggestions;
    } else if (feedback.selectedSuggestionTexts?.length) {
      // Fallback: create temporary suggestion objects from text
      selectedSuggestions = feedback.selectedSuggestionTexts.map(text => ({
        id: `temp-${Math.random().toString(36).substr(2, 9)}`,
        text,
        type: 'general' as const
      }));
    }

    let prompt = `Given the following proposal:\n\n${originalProposal}\n\n`;

    if (selectedSuggestions.length > 0) {
      prompt += `Please revise it based on these suggestions. For each suggestion, I'll provide an ID, text, and type.\n`;
      selectedSuggestions.forEach(suggestion => {
        prompt += `- [${suggestion.type || 'suggestion'}] ID: ${suggestion.id}\n  ${suggestion.text}\n`;
      });
      prompt += '\n';
    } else {
      prompt += `Please review and improve the proposal generally, focusing on clarity, conciseness, and impact.\n\n`;
    }

    prompt += `Return only the revised proposal text.`;

    try {
      const llmResponse = await ai.generate({
        prompt: prompt,

        config: {
          temperature: 0.7, // Adjust as needed
        },
      });

      const amendedText = llmResponse.text;

      if (!amendedText) {
        throw new Error('AI model did not return any text.');
      }

      // Prepare the response with both amended text and applied suggestions
      const response = {
        amendedText,
        appliedSuggestions: selectedSuggestions
      };

      // Validate the output against the schema before returning
      const validationResult = amendedProposalSchema.safeParse(response);
      if (!validationResult.success) {
        console.error('Validation failed for amended proposal:', validationResult.error.flatten());
        throw new Error(`AI model output failed validation: ${validationResult.error.message}`);
      }

      return validationResult.data;
    } catch (error) {
      console.error('Error in amendProposalFlow:', error);
      // Consider how to propagate the error. For now, re-throwing.
      // Depending on Genkit's error handling, this might be sufficient
      // or you might want to return a specific error structure.
      if (error instanceof Error) {
        throw new Error(`Failed to amend proposal: ${error.message}`);
      }
      throw new Error('Failed to amend proposal due to an unknown error.');
    }
  }
);
