/**
 * Test script for Brave Search API
 * Run with: node scripts/test-brave-search-api.js
 */

require('dotenv').config();

async function testBraveSearch() {
  const apiKey = process.env.BRAVE_API_KEY;
  
  if (!apiKey) {
    console.error('❌ BRAVE_API_KEY not found in environment variables');
    process.exit(1);
  }

  console.log('🔍 Testing Brave Search API...');
  console.log(`📝 API Key: ${apiKey.substring(0, 10)}...`);

  const query = 'artificial intelligence trends 2024';
  
  try {
    const params = new URLSearchParams({
      q: query,
      count: '5',
      offset: '0',
      mkt: 'en-US',
      safesearch: 'moderate',
      text_decorations: 'true',
      spellcheck: 'true',
    });

    console.log(`🌐 Searching for: "${query}"`);
    console.log(`📡 Request URL: https://api.search.brave.com/res/v1/web/search?${params}`);

    const response = await fetch(`https://api.search.brave.com/res/v1/web/search?${params}`, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'Accept-Encoding': 'gzip',
        'X-Subscription-Token': apiKey,
      },
    });

    console.log(`📊 Response Status: ${response.status} ${response.statusText}`);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ API Error Response:', errorText);
      throw new Error(`Brave API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    
    console.log('\n✅ Search Results:');
    console.log('==================');
    
    if (data.web && data.web.results) {
      console.log(`📈 Found ${data.web.results.length} results`);
      
      data.web.results.slice(0, 3).forEach((result, index) => {
        console.log(`\n${index + 1}. ${result.title}`);
        console.log(`   🔗 ${result.url}`);
        console.log(`   📄 ${result.description?.substring(0, 100)}...`);
      });
    } else {
      console.log('⚠️  No web results found');
    }

    // Show additional data if available
    if (data.query) {
      console.log(`\n🔍 Query Info:`);
      console.log(`   Original: ${data.query.original}`);
      console.log(`   Altered: ${data.query.altered || 'None'}`);
    }

    if (data.mixed && data.mixed.main) {
      console.log(`\n📊 Mixed Results: ${data.mixed.main.length} items`);
    }

    console.log('\n🎉 Brave Search API test completed successfully!');
    
  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    
    if (error.message.includes('401')) {
      console.error('💡 This might be an authentication issue. Check your API key.');
    } else if (error.message.includes('429')) {
      console.error('💡 Rate limit exceeded. Wait a moment and try again.');
    } else if (error.message.includes('fetch')) {
      console.error('💡 Network error. Check your internet connection.');
    }
    
    process.exit(1);
  }
}

// Run the test
testBraveSearch();
