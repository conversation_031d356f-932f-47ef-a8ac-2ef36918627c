import React, { useState, useEffect } from 'react';
import { useMutation } from '@tanstack/react-query';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast'; // Corrected path
import ProposalDisplayCard from './ProposalDisplayCard'; // Default import
import ProposalRatingControl from './ProposalRatingControl'; // Default import
import SuggestionsList from './SuggestionsList'; // Default import
import { ISuggestion, IProposal } from '@/types/proposal'; // Assuming IProposal might be useful for API response type

interface IProposalAmendmentSectionProps {
  initialProposalText: string;
  initialSuggestions: ISuggestion[];
  // Add any other props that might be passed from the parent, e.g., proposalId
  proposalId?: string;
  appliedSuggestions: ISuggestion[];
  onAppliedSuggestionsChange: (suggestions: ISuggestion[]) => void;
  onProposalTextChange?: (text: string) => void; // Callback for when proposal text changes
}

interface IAmendProposalPayload {
  originalProposal: string;
  feedback: {
    score?: number;
    selectedSuggestionTexts?: string[];
    selectedSuggestions?: ISuggestion[]; // Added for full suggestion objects
  };
  proposalId?: string; // Optional, if needed for the API
}

// Simulate API response type, adjust as per actual API
interface IAmendProposalResponse extends IProposal {
  // Assuming the API returns the amended proposal, score, and new suggestions
  amendedText: string;
  newScore?: number;
  newSuggestions?: ISuggestion[];
  appliedSuggestions?: ISuggestion[]; // Added for applied suggestions
}

const ProposalAmendmentSection: React.FC<IProposalAmendmentSectionProps> = ({
  initialProposalText,
  initialSuggestions,
  proposalId,
  appliedSuggestions,
  onAppliedSuggestionsChange,
  onProposalTextChange,
}) => {
  const { toast } = useToast();

  const [currentProposalText, setCurrentProposalText] = useState<string>(initialProposalText);
  const [amendedProposalText, setAmendedProposalText] = useState<string | null>(null);
  const [score, setScore] = useState<number | null>(null);
  const [selectedSuggestionIds, setSelectedSuggestionIds] = useState<string[]>([]);
  const [selectedFullSuggestions, setSelectedFullSuggestions] = useState<ISuggestion[]>([]);
  // Applied suggestions are now managed by the parent component
  const [currentSuggestions, setCurrentSuggestions] = useState<ISuggestion[]>(initialSuggestions);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    setCurrentProposalText(initialProposalText);
    // Filter out any suggestions that have already been applied
    const filteredSuggestions = initialSuggestions.filter(
      suggestion => !appliedSuggestions.some(applied => applied.id === suggestion.id)
    );
    setCurrentSuggestions(filteredSuggestions);
    setAmendedProposalText(null); // Reset amended text when initial proposal changes
    // Don't reset score anymore
    setSelectedSuggestionIds([]); // Reset selections
  }, [initialProposalText, initialSuggestions, appliedSuggestions]);

  const amendProposalMutation = useMutation<IAmendProposalResponse, Error, IAmendProposalPayload>({
    mutationFn: async (payload: IAmendProposalPayload) => {
      setIsLoading(true);
      setError(null);
      
      // Simulate API call
      try {
        const response = await fetch('/api/proposal/amend', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            originalProposal: payload.originalProposal,
            feedback: {
              score: payload.feedback.score,
              selectedSuggestionTexts: payload.feedback.selectedSuggestionTexts || [],
              selectedSuggestions: payload.feedback.selectedSuggestions || []
            },
            proposalId: payload.proposalId
          }),
        });
        
        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.message || 'Failed to amend proposal');
        }
        
        const result = await response.json();
        
        // Notify parent of the new proposal text
        if (onProposalTextChange) {
          onProposalTextChange(result.amendedText);
        }
        
        // Transform the API response to match IAmendProposalResponse
        return {
          id: proposalId || 'new-id',
          title: 'Amended Proposal',
          text: result.amendedText,
          amendedText: result.amendedText,
          // Preserve the current score
          score: payload.feedback.score,
          newScore: payload.feedback.score, // Keep the same score
          // Pass back the suggestions that were sent to the API as 'appliedSuggestions'
          // This assumes the backend successfully applied them.
          appliedSuggestions: payload.feedback.selectedSuggestions || [],
          // Remove applied suggestions from new suggestions
          newSuggestions: [],
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        };
      } catch (error) {
        console.error('Error amending proposal:', error);
        throw new Error('Failed to amend proposal. Please try again.');
      }
    },
      onSuccess: (data: IAmendProposalResponse) => {
        setAmendedProposalText(data.amendedText);
        
        // Store applied suggestions from the API response
        const newAppliedSuggestions = data.appliedSuggestions || [];
        onAppliedSuggestionsChange([...appliedSuggestions, ...newAppliedSuggestions]);
        
        // Clear selected suggestions for the next round
        setSelectedFullSuggestions([]);
        setSelectedSuggestionIds([]);
        
        // Remove applied suggestions from current suggestions list
        const appliedSuggestionIds = newAppliedSuggestions.map(suggestion => suggestion.id);
        const remainingSuggestions = currentSuggestions.filter(
          suggestion => !appliedSuggestionIds.includes(suggestion.id)
        );
        
        // Update suggestions with remaining ones plus any new ones
        setCurrentSuggestions(remainingSuggestions);
        
        // Don't update score - keep the existing one
        // This preserves the user's rating
        
        toast({
          title: 'Proposal Amended',
          description: 'The proposal has been successfully amended.',
        });
      },
      onError: (err: Error) => {
        setError(err.message);
        toast({
          title: 'Error Amending Proposal',
          description: err.message,
          variant: 'destructive',
        });
      },
      onSettled: () => {
        setIsLoading(false);
      },
    }
  );

  const handleAmendProposal = () => {
    if (score === null) {
      toast({
        title: 'Rating Required',
        description: 'Please rate the proposal before amending.',
        variant: 'destructive',
      });
      return;
    }
    
    // Get the selected suggestion texts from selectedFullSuggestions
    const selectedSuggestionTexts = selectedFullSuggestions.map(suggestion => suggestion.text);

    const payload: IAmendProposalPayload = {
      originalProposal: currentProposalText,
      feedback: {
        score: score,
        selectedSuggestionTexts: selectedSuggestionTexts.length > 0 ? selectedSuggestionTexts : undefined,
        selectedSuggestions: selectedFullSuggestions.length > 0 ? selectedFullSuggestions : undefined
      }
    };
    
    amendProposalMutation.mutate(payload);
  };

  const handleSuggestionToggle = (suggestionId: string) => {
    setSelectedSuggestionIds((prevIds) => {
      const newSelectedIds = prevIds.includes(suggestionId)
        ? prevIds.filter((id) => id !== suggestionId)
        : [...prevIds, suggestionId];
      
      // Update selectedFullSuggestions based on the new selection
      const newSelectedSuggestions = currentSuggestions.filter(suggestion => 
        newSelectedIds.includes(suggestion.id)
      );
      setSelectedFullSuggestions(newSelectedSuggestions);
      
      return newSelectedIds;
    });
  };

  return (
    <div className="space-y-6 p-4 border rounded-lg shadow-sm">
      <h2 className="text-2xl font-semibold">Amend Proposal</h2>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <h3 className="text-xl font-medium mb-2">Current Proposal</h3>
          <ProposalDisplayCard proposalText={currentProposalText} title="Current Version" />
        </div>
        {amendedProposalText && (
          <div>
            <h3 className="text-xl font-medium mb-2">Amended Proposal</h3>
            <ProposalDisplayCard proposalText={amendedProposalText} title="Amended Version" />
          </div>
        )}
      </div>

      <ProposalRatingControl currentScore={score} onScoreChange={setScore} />

      {/* Applied Suggestions Feedback */}
      {appliedSuggestions.length > 0 && (
        <div className="mt-4 p-4 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800">
          <h3 className="text-lg font-medium text-green-800 dark:text-green-200 mb-2">Applied Suggestions</h3>
          <p className="text-sm text-green-700 dark:text-green-300 mb-3">
            The following suggestions were considered in the most recent amendment:
          </p>
          <ul className="space-y-2">
            {appliedSuggestions.map((suggestion) => (
              <li 
                key={suggestion.id} 
                className="flex items-start p-3 bg-white dark:bg-gray-800 rounded-md border border-green-100 dark:border-green-900/50"
              >
                <svg 
                  className="h-5 w-5 text-green-500 mr-2 mt-0.5 flex-shrink-0" 
                  fill="none" 
                  viewBox="0 0 24 24" 
                  stroke="currentColor"
                >
                  <path 
                    strokeLinecap="round" 
                    strokeLinejoin="round" 
                    strokeWidth={2} 
                    d="M5 13l4 4L19 7" 
                  />
                </svg>
                <span className="text-sm text-gray-700 dark:text-gray-300">{suggestion.text}</span>
              </li>
            ))}
          </ul>
        </div>
      )}

      {currentSuggestions.length > 0 && (
        <SuggestionsList
          suggestions={currentSuggestions}
          selectedSuggestionIds={selectedSuggestionIds}
          onSuggestionSelect={handleSuggestionToggle} // Prop name updated from onSuggestionToggle to onSuggestionSelect to match SuggestionsList component
        />
      )}

      {error && <p className="text-red-500">Error: {error}</p>}

      <Button onClick={handleAmendProposal} disabled={isLoading || score === null}>
        {isLoading ? 'Amending...' : 'Amend Proposal'}
      </Button>

      {/* For iterative refinement, user might want to make the amended version the new current version */}
      {amendedProposalText && (
        <Button 
          variant="outline" 
          className="ml-4"
          onClick={() => {
            setCurrentProposalText(amendedProposalText);
            setAmendedProposalText(null);
            // Potentially reset score and suggestions or use new ones if API provides them
            // For now, we keep currentSuggestions, user can re-rate
            toast({ title: 'Amended proposal set as current.'});
          }}
        >
          Use Amended Version as Current
        </Button>
      )}
    </div>
  );
};

export { ProposalAmendmentSection };
