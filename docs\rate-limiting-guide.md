# Brave Search API Rate Limiting Guide

This guide explains how to manage Brave Search API rate limits and optimize your usage in ProposalPilot.

## Understanding Rate Limits

### Brave Search API Limits

**Free Tier:**
- **2,000 queries per month**
- **Rate limits**: Varies by plan, typically 1-10 requests per second
- **Burst limits**: Short-term higher rates allowed

**Paid Tiers:**
- Higher monthly quotas (10K, 100K, 1M+ queries)
- Higher rate limits
- Priority support

## Rate Limiting in ProposalPilot

### Built-in Protection

ProposalPilot includes several mechanisms to prevent rate limit issues:

1. **Request Throttling**: Minimum 1-second delay between requests
2. **Intelligent Fallbacks**: Domain-specific knowledge when limits are hit
3. **Error Handling**: Graceful degradation with meaningful responses

### Rate Limiting Implementation

```typescript
// Automatic rate limiting in MCP client
private minRequestInterval = 1000; // 1 second between requests

private async waitForRateLimit(): Promise<void> {
  const now = Date.now();
  const timeSinceLastRequest = now - this.lastRequestTime;
  
  if (timeSinceLastRequest < this.minRequestInterval) {
    const waitTime = this.minRequestInterval - timeSinceLastRequest;
    await new Promise(resolve => setTimeout(resolve, waitTime));
  }
  
  this.lastRequestTime = Date.now();
}
```

## Managing Your API Usage

### 1. Monitor Your Usage

Check your API usage at: https://api-dashboard.search.brave.com/

- **Current usage**: See how many queries you've used
- **Rate limit status**: Check if you're hitting limits
- **Usage patterns**: Understand peak usage times

### 2. Optimize Query Frequency

**Best Practices:**
- Generate proposals in batches rather than one-by-one
- Use specific, targeted search queries
- Avoid regenerating the same proposals repeatedly during testing

**Development Tips:**
- Use the fallback mode during development by commenting out your API key
- Test with simulated research first, then enable real search for final testing

### 3. Upgrade Your Plan

If you frequently hit rate limits:

1. **Assess your needs**: How many proposals do you generate per month?
2. **Calculate queries**: Each proposal typically uses 1-3 search queries
3. **Choose appropriate tier**: 
   - **Free**: Up to ~600-1000 proposals/month
   - **Paid**: 3K+ proposals/month depending on tier

## Fallback Behavior

When rate limits are exceeded, ProposalPilot provides intelligent fallbacks:

### Domain-Specific Knowledge

- **Cloud Computing**: AWS, Azure, GCP best practices
- **AI/ML**: Current trends in LLMs, MLOps, responsible AI
- **Security**: Zero Trust, DevSecOps, compliance frameworks
- **Data**: Modern data architecture, analytics trends

### Example Fallback Response

```
Rate limit reached. Based on current cloud computing trends: Multi-cloud 
strategies, serverless architectures, and Infrastructure as Code (IaC) are 
key priorities. Consider AWS Well-Architected Framework, Azure Cloud Adoption 
Framework, or Google Cloud Architecture Framework for best practices.
```

## Troubleshooting Rate Limits

### Common Scenarios

1. **"Rate limit exceeded" during proposal generation**
   - **Solution**: Wait 1-2 minutes and try again
   - **Prevention**: Space out proposal generations

2. **Multiple rate limit errors in succession**
   - **Solution**: Check your API dashboard for usage
   - **Prevention**: Implement longer delays between requests

3. **Consistent rate limiting**
   - **Solution**: Consider upgrading your API plan
   - **Alternative**: Use fallback mode for development

### Configuration Options

You can adjust rate limiting in `src/lib/mcp-client.ts`:

```typescript
// Increase delay between requests (in milliseconds)
private minRequestInterval = 2000; // 2 seconds instead of 1

// Or disable rate limiting for paid plans with higher limits
private minRequestInterval = 100; // 100ms for high-tier plans
```

## Best Practices

### For Development

1. **Use fallback mode**: Comment out `BRAVE_API_KEY` during development
2. **Test incrementally**: Enable real search only for final testing
3. **Cache results**: Save generated proposals to avoid regenerating

### For Production

1. **Monitor usage**: Set up alerts for approaching limits
2. **Implement caching**: Store search results for common queries
3. **Batch operations**: Generate multiple proposals in planned sessions

### For High-Volume Usage

1. **Upgrade API plan**: Get higher rate limits and quotas
2. **Implement request queuing**: Queue requests during peak times
3. **Use multiple API keys**: Distribute load across multiple accounts (if allowed)

## Error Messages and Solutions

| Error Message | Cause | Solution |
|---------------|-------|----------|
| "Rate limit exceeded" | Too many requests too quickly | Wait and retry |
| "Monthly quota exceeded" | Used all monthly queries | Upgrade plan or wait for reset |
| "Invalid API key" | Wrong or expired key | Check API key in dashboard |
| "MCP connection failed" | Network or server issue | Check internet connection |

## Monitoring and Alerts

### Set Up Monitoring

1. **API Dashboard**: Regular checks of usage
2. **Application Logs**: Monitor rate limit warnings
3. **User Feedback**: Track when users encounter issues

### Usage Patterns

Track these metrics:
- Proposals generated per day/week/month
- Average search queries per proposal
- Peak usage times
- Rate limit frequency

## Support and Resources

- **Brave Search API Docs**: https://brave.com/search/api/
- **API Dashboard**: https://api-dashboard.search.brave.com/
- **ProposalPilot Issues**: Report rate limiting issues in your project repository

## Summary

Rate limiting is a normal part of API usage. ProposalPilot handles it gracefully with:

✅ **Automatic throttling** to prevent hitting limits
✅ **Intelligent fallbacks** with domain-specific knowledge  
✅ **Clear error messages** explaining what happened
✅ **Graceful degradation** so proposals still generate

The system is designed to work well within free tier limits while providing upgrade paths for higher usage needs.
