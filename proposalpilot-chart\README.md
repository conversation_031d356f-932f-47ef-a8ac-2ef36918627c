# ProposalPilot Helm Chart

This chart deploys the ProposalPilot application along with its required dependencies.

## Prerequisites

- Kubernetes 1.23+
- Helm 3.8.0+
- PV provisioner support in the underlying infrastructure
- Docker Desktop with Kubernetes enabled (for local development)

## Installing the Chart

### Add the Bitnami repository (if not already added)

```bash
helm repo add bitnami https://charts.bitnami.com/bitnami
helm repo update
```

### Install with default values

```bash
# Install or upgrade the release
helm upgrade --install proposalpilot ./proposalpilot-chart -n proposalpilot --create-namespace \
  --set secrets.geminiApiKey="your-gemini-api-key" \
  --set secrets.braveApiKey="your-brave-api-key" \
  --set secrets.serperApiKey="your-serper-api-key" \
  --set secrets.postgresPassword="your-strong-db-password" \
  --set secrets.postgresPostgresPassword="your-strong-postgres-password"
```

### Accessing the application

After installation, you can access the application using port-forwarding:

```bash
kubectl port-forward -n proposalpilot svc/proposalpilot 3000:3000
```

Then open http://localhost:3000 in your browser.

## Configuration

The following table lists the configurable parameters of the ProposalPilot chart and their default values.

### Application Configuration

| Parameter | Description | Default |
|-----------|-------------|---------|
| `replicaCount` | Number of application replicas | `1` |
| `image.repository` | Application image repository | `proposalpilot` |
| `image.tag` | Application image tag | `latest` |
| `service.type` | Kubernetes service type | `ClusterIP` |
| `service.port` | Service port | `3000` |

### PostgreSQL Configuration

| Parameter | Description | Default |
|-----------|-------------|---------|
| `postgresql.enabled` | Enable PostgreSQL | `true` |
| `postgresql.auth.database` | Database name | `proposalpilot` |
| `postgresql.auth.username` | Database user | `proposalpilot` |
| `postgresql.auth.password` | Database password (set via secrets) | `""` |
| `postgresql.auth.postgresPassword` | PostgreSQL admin password (set via secrets) | `""` |
| `postgresql.primary.persistence.size` | Persistent volume size | `8Gi` |

### Secrets

Secrets should be provided during installation using the `--set` flag or a custom values file. The following secrets are required:

- `secrets.geminiApiKey`: Gemini API key
- `secrets.braveApiKey`: Brave Search API key
- `secrets.serperApiKey`: Serper API key
- `secrets.postgresPassword`: Database user password
- `secrets.postgresPostgresPassword`: PostgreSQL admin password

## Upgrading the Chart

To upgrade the release with a new version of the chart or application:

```bash
helm upgrade proposalpilot ./proposalpilot-chart -n proposalpilot \
  --set image.tag=new-version \
  --reuse-values
```

## Uninstalling the Chart

To uninstall/delete the `proposalpilot` deployment:

```bash
helm uninstall proposalpilot -n proposalpilot
```

This command removes all the Kubernetes components associated with the chart and deletes the release.

## Persistence

The PostgreSQL data is stored in a Persistent Volume (PV) by default. The volume is created using dynamic volume provisioning. When deleting the release, the associated PVC will remain in the cluster. To delete the PVC:

```bash
kubectl delete pvc -n proposalpilot data-proposalpilot-postgresql-0
```
