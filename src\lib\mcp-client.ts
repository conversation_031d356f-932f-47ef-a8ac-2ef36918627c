/**
 * @fileOverview MCP Client for Brave Search integration
 *
 * This module provides a client interface to communicate with the Brave Search MCP server.
 * It handles the connection, tool execution, and response parsing.
 */

import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js';

export interface BraveSearchResult {
  title: string;
  url: string;
  description: string;
  age?: string;
  language?: string;
}

export interface BraveLocalSearchResult {
  title: string;
  url: string;
  description: string;
  address?: string;
  phone?: string;
  rating?: number;
}

export interface BraveWebSearchResponse {
  results: BraveSearchResult[];
  query: string;
  total_count?: number;
}

export interface BraveLocalSearchResponse {
  results: BraveLocalSearchResult[];
  query: string;
  total_count?: number;
}

class MCPBraveSearchClient {
  private client: Client | null = null;
  private transport: StdioClientTransport | null = null;
  private isConnected = false;
  private lastRequestTime = 0;
  private minRequestInterval = 3000; // Minimum 3 seconds between requests to avoid rate limits
  private requestQueue: Array<() => Promise<any>> = [];
  private isProcessingQueue = false;
  private rateLimitResetTime = 0;
  private consecutiveRateLimitErrors = 0;
  private maxRetries = 3;

  /**
   * Initialize the MCP client connection to Brave Search server
   */
  async connect(): Promise<void> {
    if (this.isConnected) {
      return;
    }

    try {
      // Create transport for the Brave Search MCP server
      this.transport = new StdioClientTransport({
        command: 'npx',
        args: ['-y', '@modelcontextprotocol/server-brave-search'],
        env: {
          ...process.env,
          BRAVE_API_KEY: process.env.BRAVE_API_KEY || '',
        },
      });

      this.client = new Client(
        {
          name: 'proposalpilot-brave-search-client',
          version: '1.0.0',
        },
        {
          capabilities: {
            tools: {},
          },
        }
      );

      await this.client.connect(this.transport);
      this.isConnected = true;
      console.log('Connected to Brave Search MCP server');
    } catch (error) {
      console.error('Failed to connect to Brave Search MCP server:', error);
      throw new Error(`MCP connection failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Disconnect from the MCP server
   */
  async disconnect(): Promise<void> {
    if (this.client && this.isConnected) {
      await this.client.close();
      this.client = null;
      this.transport = null;
      this.isConnected = false;
      console.log('Disconnected from Brave Search MCP server');
    }
  }

  /**
   * Rate limiting helper to prevent hitting API limits
   */
  private async waitForRateLimit(): Promise<void> {
    const now = Date.now();

    // If we're in a rate limit cooldown period, wait longer
    if (now < this.rateLimitResetTime) {
      const waitTime = this.rateLimitResetTime - now;
      console.log(`Rate limit cooldown: waiting ${waitTime}ms before next request`);
      await new Promise(resolve => setTimeout(resolve, waitTime));
    }

    const timeSinceLastRequest = now - this.lastRequestTime;
    const adjustedInterval = this.minRequestInterval * Math.pow(2, Math.min(this.consecutiveRateLimitErrors, 3));

    if (timeSinceLastRequest < adjustedInterval) {
      const waitTime = adjustedInterval - timeSinceLastRequest;
      console.log(`Rate limiting: waiting ${waitTime}ms before next request (adjusted for ${this.consecutiveRateLimitErrors} consecutive errors)`);
      await new Promise(resolve => setTimeout(resolve, waitTime));
    }

    this.lastRequestTime = Date.now();
  }

  /**
   * Check if an error is a rate limit error
   */
  private isRateLimitError(error: any): boolean {
    const errorMessage = error instanceof Error ? error.message : String(error);
    return errorMessage.includes('Rate limit exceeded') ||
           errorMessage.includes('429') ||
           errorMessage.includes('Too Many Requests') ||
           errorMessage.includes('RATE_LIMITED');
  }

  /**
   * Handle rate limit errors with exponential backoff
   */
  private handleRateLimitError(): void {
    this.consecutiveRateLimitErrors++;
    // Set cooldown period: 30 seconds for first error, increasing exponentially
    const cooldownMs = 30000 * Math.pow(2, Math.min(this.consecutiveRateLimitErrors - 1, 4));
    this.rateLimitResetTime = Date.now() + cooldownMs;
    console.log(`Rate limit error #${this.consecutiveRateLimitErrors}. Setting cooldown for ${cooldownMs}ms`);
  }

  /**
   * Reset rate limit error tracking on successful request
   */
  private resetRateLimitTracking(): void {
    if (this.consecutiveRateLimitErrors > 0) {
      console.log(`Successful request after ${this.consecutiveRateLimitErrors} rate limit errors. Resetting tracking.`);
      this.consecutiveRateLimitErrors = 0;
      this.rateLimitResetTime = 0;
    }
  }

  /**
   * Perform a web search using Brave Search with retry logic
   */
  async webSearch(query: string, count: number = 10, offset: number = 0): Promise<BraveWebSearchResponse> {
    return this.executeWithRetry(async () => {
      // Apply rate limiting
      await this.waitForRateLimit();

      if (!this.client || !this.isConnected) {
        await this.connect();
      }

      if (!this.client) {
        throw new Error('Failed to establish MCP connection');
      }

      const result = await this.client.callTool({
        name: 'brave_web_search',
        arguments: {
          query,
          count: Math.min(count, 20), // Brave Search API limit
          offset: Math.min(offset, 9), // Brave Search API limit
        },
      });

      if (result.isError) {
        const errorContent = result.content as any[];
        const errorText = errorContent[0]?.text || 'Unknown error';
        throw new Error(`Brave Search error: ${errorText}`);
      }

      // Parse the response from the MCP server
      const content = result.content as any[];
      const responseText = content[0]?.text || '{}';

      // Handle different response formats from the MCP server
      let parsedResponse;
      try {
        parsedResponse = JSON.parse(responseText);
      } catch (parseError) {
        // If JSON parsing fails, the response might be in plain text format
        console.log('Raw response text:', responseText.substring(0, 200) + '...');

        // For now, return a structured response with the raw text
        parsedResponse = {
          results: [{
            title: 'Search Results',
            url: '',
            description: responseText.substring(0, 500) + '...'
          }]
        };
      }

      // Mark successful request
      this.resetRateLimitTracking();

      return {
        results: parsedResponse.results || [],
        query,
        total_count: parsedResponse.total_count,
      };
    });
  }

  /**
   * Execute a function with retry logic for rate limit errors
   */
  private async executeWithRetry<T>(fn: () => Promise<T>): Promise<T> {
    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= this.maxRetries; attempt++) {
      try {
        return await fn();
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));

        if (this.isRateLimitError(error)) {
          this.handleRateLimitError();

          if (attempt < this.maxRetries) {
            const backoffMs = 1000 * Math.pow(2, attempt - 1); // Exponential backoff: 1s, 2s, 4s
            console.log(`Rate limit error on attempt ${attempt}/${this.maxRetries}. Retrying in ${backoffMs}ms...`);
            await new Promise(resolve => setTimeout(resolve, backoffMs));
            continue;
          } else {
            console.log(`Rate limit error on final attempt ${attempt}/${this.maxRetries}. Giving up.`);
            // For rate limit errors on final attempt, throw a specific error that can be caught
            throw new Error(`RATE_LIMIT_EXCEEDED: ${lastError.message}`);
          }
        } else {
          // For non-rate-limit errors, don't retry
          console.error(`Non-rate-limit error on attempt ${attempt}:`, error);
          throw lastError;
        }
      }
    }

    throw lastError || new Error('Unknown error during retry execution');
  }

  /**
   * Perform a local search using Brave Search with retry logic
   */
  async localSearch(query: string, count: number = 10): Promise<BraveLocalSearchResponse> {
    return this.executeWithRetry(async () => {
      // Apply rate limiting
      await this.waitForRateLimit();

      if (!this.client || !this.isConnected) {
        await this.connect();
      }

      if (!this.client) {
        throw new Error('Failed to establish MCP connection');
      }

      const result = await this.client.callTool({
        name: 'brave_local_search',
        arguments: {
          query,
          count: Math.min(count, 20), // Brave Search API limit
        },
      });

      if (result.isError) {
        const errorContent = result.content as any[];
        const errorText = errorContent[0]?.text || 'Unknown error';
        throw new Error(`Brave Local Search error: ${errorText}`);
      }

      // Parse the response from the MCP server
      const content = result.content as any[];
      const responseText = content[0]?.text || '{}';

      // Handle different response formats from the MCP server
      let parsedResponse;
      try {
        parsedResponse = JSON.parse(responseText);
      } catch (parseError) {
        // If JSON parsing fails, the response might be in plain text format
        console.log('Raw local search response text:', responseText.substring(0, 200) + '...');

        // For now, return a structured response with the raw text
        parsedResponse = {
          results: [{
            title: 'Local Search Results',
            url: '',
            description: responseText.substring(0, 500) + '...'
          }]
        };
      }

      // Mark successful request
      this.resetRateLimitTracking();

      return {
        results: parsedResponse.results || [],
        query,
        total_count: parsedResponse.total_count,
      };
    });
  }

  /**
   * Add a request to the queue and process it
   */
  async queueRequest<T>(requestFn: () => Promise<T>): Promise<T> {
    return new Promise((resolve, reject) => {
      this.requestQueue.push(async () => {
        try {
          const result = await requestFn();
          resolve(result);
        } catch (error) {
          reject(error);
        }
      });

      this.processQueue();
    });
  }

  /**
   * Process the request queue sequentially
   */
  private async processQueue(): Promise<void> {
    if (this.isProcessingQueue || this.requestQueue.length === 0) {
      return;
    }

    this.isProcessingQueue = true;

    while (this.requestQueue.length > 0) {
      const request = this.requestQueue.shift();
      if (request) {
        try {
          await request();
        } catch (error) {
          console.error('Error processing queued request:', error);
        }
      }
    }

    this.isProcessingQueue = false;
  }

  /**
   * Check if the client is connected
   */
  isClientConnected(): boolean {
    return this.isConnected;
  }
}

// Export a singleton instance with connection management
let _braveSearchClientInstance: MCPBraveSearchClient | null = null;

export function getBraveSearchClient(): MCPBraveSearchClient {
  if (!_braveSearchClientInstance) {
    _braveSearchClientInstance = new MCPBraveSearchClient();
  }
  return _braveSearchClientInstance;
}

// For backward compatibility
export const braveSearchClient = getBraveSearchClient();

/**
 * Cleanup function to disconnect and reset the client instance
 * Call this when shutting down the application or when you want to reset connections
 */
export async function cleanupBraveSearchClient(): Promise<void> {
  if (_braveSearchClientInstance) {
    try {
      await _braveSearchClientInstance.disconnect();
    } catch (error) {
      console.error('Error during MCP client cleanup:', error);
    } finally {
      _braveSearchClientInstance = null;
    }
  }
}

// Cleanup on process exit
if (typeof process !== 'undefined') {
  process.on('exit', () => {
    cleanupBraveSearchClient().catch(console.error);
  });

  process.on('SIGINT', () => {
    cleanupBraveSearchClient().then(() => process.exit(0)).catch(() => process.exit(1));
  });

  process.on('SIGTERM', () => {
    cleanupBraveSearchClient().then(() => process.exit(0)).catch(() => process.exit(1));
  });
}

/**
 * Utility function to perform web search with automatic connection management and queuing
 */
export async function performBraveWebSearch(
  query: string,
  count: number = 10,
  offset: number = 0
): Promise<BraveWebSearchResponse> {
  const client = getBraveSearchClient();

  // Use the queue to ensure sequential processing and proper rate limiting
  return client.queueRequest(async () => {
    try {
      return await client.webSearch(query, count, offset);
    } catch (error) {
      console.error('Queued web search failed:', error);
      throw error;
    }
  });
}

/**
 * Utility function to perform local search with automatic connection management and queuing
 */
export async function performBraveLocalSearch(
  query: string,
  count: number = 10
): Promise<BraveLocalSearchResponse> {
  const client = getBraveSearchClient();

  // Use the queue to ensure sequential processing and proper rate limiting
  return client.queueRequest(async () => {
    try {
      return await client.localSearch(query, count);
    } catch (error) {
      console.error('Queued local search failed:', error);
      throw error;
    }
  });
}
