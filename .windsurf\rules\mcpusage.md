---
trigger: always_on
---

The following MCPs are available to you as tools and function when required:

# Sequential Thinking MCP
- Use Sequential Thinking MCP primarily for debugging, troubleshooting, complex problem-solving, and detailed project planning.
- Trigger recursive calls judiciously—only when new progress or significant information is expected to avoid performance degradation and 
  infinite loops.

