"use client";

import React from 'react';
import { KeyRound } from 'lucide-react';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';

interface UserPreferencesSectionProps {
  preferredTechnologies: string;
  technicalConstraints: string;
  budgetConsiderations: string;
  timelineConstraints: string;
  complianceRequirements: string;
  integrationRequirements: string;
  scalabilityRequirements: string;
  securityRequirements: string;
  additionalConsiderations: string;
  onPreferredTechnologiesChange: (value: string) => void;
  onTechnicalConstraintsChange: (value: string) => void;
  onBudgetConsiderationsChange: (value: string) => void;
  onTimelineConstraintsChange: (value: string) => void;
  onComplianceRequirementsChange: (value: string) => void;
  onIntegrationRequirementsChange: (value: string) => void;
  onScalabilityRequirementsChange: (value: string) => void;
  onSecurityRequirementsChange: (value: string) => void;
  onAdditionalConsiderationsChange: (value: string) => void;
}

export const UserPreferencesSection: React.FC<UserPreferencesSectionProps> = ({
  preferredTechnologies,
  technicalConstraints,
  budgetConsiderations,
  timelineConstraints,
  complianceRequirements,
  integrationRequirements,
  scalabilityRequirements,
  securityRequirements,
  additionalConsiderations,
  onPreferredTechnologiesChange,
  onTechnicalConstraintsChange,
  onBudgetConsiderationsChange,
  onTimelineConstraintsChange,
  onComplianceRequirementsChange,
  onIntegrationRequirementsChange,
  onScalabilityRequirementsChange,
  onSecurityRequirementsChange,
  onAdditionalConsiderationsChange,
}) => {
  return (
    <div className="space-y-4">
      <div className="flex items-center space-x-3">
        <KeyRound className="w-7 h-7 text-primary" />
        <div>
          <h2 className="text-xl font-semibold">1.5. Technology Preferences & Considerations</h2>
          <p className="text-sm text-muted-foreground">
            Specify your preferred technologies, constraints, and other considerations to guide the AI agents in generating tailored proposals.
          </p>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-4">
          <div>
            <Label htmlFor="preferredTechnologies">Preferred Technologies</Label>
            <Textarea
              id="preferredTechnologies"
              value={preferredTechnologies}
              onChange={(e) => onPreferredTechnologiesChange(e.target.value)}
              placeholder="e.g., Preferred Platforms, Cloud Services, Frameworks (comma-separated)"
              className="min-h-[80px] text-sm"
            />
          </div>

          <div>
            <Label htmlFor="technicalConstraints">Technical Constraints</Label>
            <Textarea
              id="technicalConstraints"
              value={technicalConstraints}
              onChange={(e) => onTechnicalConstraintsChange(e.target.value)}
              placeholder="e.g., Must use on-premise infrastructure, No cloud services (comma-separated)"
              className="min-h-[80px] text-sm"
            />
          </div>

          <div>
            <Label htmlFor="budgetConsiderations">Budget Considerations</Label>
            <Textarea
              id="budgetConsiderations"
              value={budgetConsiderations}
              onChange={(e) => onBudgetConsiderationsChange(e.target.value)}
              placeholder="e.g., Limited budget, prefer open-source solutions, cost-effective approach"
              className="min-h-[60px] text-sm"
            />
          </div>

          <div>
            <Label htmlFor="timelineConstraints">Timeline Constraints</Label>
            <Textarea
              id="timelineConstraints"
              value={timelineConstraints}
              onChange={(e) => onTimelineConstraintsChange(e.target.value)}
              placeholder="e.g., Must deliver within 6 months, phased delivery required"
              className="min-h-[60px] text-sm"
            />
          </div>
        </div>

        <div className="space-y-4">
          <div>
            <Label htmlFor="complianceRequirements">Compliance Requirements</Label>
            <Textarea
              id="complianceRequirements"
              value={complianceRequirements}
              onChange={(e) => onComplianceRequirementsChange(e.target.value)}
              placeholder="e.g., GDPR, HIPAA, SOX, ISO 27001 (comma-separated)"
              className="min-h-[80px] text-sm"
            />
          </div>

          <div>
            <Label htmlFor="integrationRequirements">Integration Requirements</Label>
            <Textarea
              id="integrationRequirements"
              value={integrationRequirements}
              onChange={(e) => onIntegrationRequirementsChange(e.target.value)}
              placeholder="e.g., SAP ERP, Salesforce, Active Directory (comma-separated)"
              className="min-h-[80px] text-sm"
            />
          </div>

          <div>
            <Label htmlFor="scalabilityRequirements">Scalability Requirements</Label>
            <Textarea
              id="scalabilityRequirements"
              value={scalabilityRequirements}
              onChange={(e) => onScalabilityRequirementsChange(e.target.value)}
              placeholder="e.g., Support 10,000 concurrent users, auto-scaling capabilities"
              className="min-h-[60px] text-sm"
            />
          </div>

          <div>
            <Label htmlFor="securityRequirements">Security Requirements</Label>
            <Textarea
              id="securityRequirements"
              value={securityRequirements}
              onChange={(e) => onSecurityRequirementsChange(e.target.value)}
              placeholder="e.g., Multi-factor authentication, encryption at rest, penetration testing (comma-separated)"
              className="min-h-[60px] text-sm"
            />
          </div>
        </div>
      </div>

      <div className="mt-4">
        <Label htmlFor="additionalConsiderations">Additional Considerations</Label>
        <Textarea
          id="additionalConsiderations"
          value={additionalConsiderations}
          onChange={(e) => onAdditionalConsiderationsChange(e.target.value)}
          placeholder="Any other specific requirements, preferences, or constraints that should guide the proposal generation..."
          className="min-h-[80px] text-sm"
        />
      </div>

      <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-md dark:bg-blue-950/20 dark:border-blue-800">
        <p className="text-sm text-blue-800 dark:text-blue-200">
          <strong>💡 Tip:</strong> These preferences will be considered by all AI agents when generating your proposal.
          For technologies and constraints, use comma-separated values. Leave fields empty if not applicable.
        </p>
      </div>
    </div>
  );
};
