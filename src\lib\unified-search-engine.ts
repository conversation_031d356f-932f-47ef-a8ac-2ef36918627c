/**
 * Unified Search Engine - Consolidated web search implementation
 * Combines the best features from research-engine, mcp-client, and multi-search-consolidator
 *
 * Features:
 * - Multiple search providers (Serper, Brave API, Brave MCP, DuckDuckGo)
 * - Intelligent fallback chain
 * - Advanced circuit breakers
 * - Parallel search execution
 * - Smart result consolidation
 * - Adaptive rate limiting
 */

export interface SearchResult {
  title: string;
  url: string;
  snippet: string;
  source: string;
  relevanceScore: number;
  timestamp: number;
  metadata?: {
    language?: string;
    age?: string;
    domain?: string;
  };
}

export interface SearchSummary {
  query: string;
  results: SearchResult[];
  totalSources: number;
  uniqueUrls: number;
  searchTime: number;
  sourcesUsed: string[];
  duplicatesRemoved: number;
  knowledgeGraph?: {
    title: string;
    description: string;
    website?: string;
  };
  performance: {
    [key: string]: number | boolean | undefined;
    serperTime?: number;
    braveTime?: number;
    mcpTime?: number;
    parallelExecution: boolean;
  };
}

export interface SearchProvider {
  name: string;
  priority: number;
  enabled: boolean;
  rateLimitDelay: number;
  maxResults: number;
  timeout: number;
  search: (query: string, maxResults: number) => Promise<SearchResult[]>;
}

export interface CircuitBreakerState {
  failures: number;
  lastFailure: number;
  isOpen: boolean;
  successCount: number;
  lastSuccess: number;
}

export interface SearchOptions {
  maxResults?: number;
  includeKnowledgeGraph?: boolean;
  timeout?: number;
  parallelExecution?: boolean;
  maxProviders?: number;
  includeBackups?: boolean;
}

export class UnifiedSearchEngine {
  private providers: Map<string, SearchProvider> = new Map();
  private rateLimits = new Map<string, number>();
  private circuitBreakers = new Map<string, CircuitBreakerState>();
  private resultCache = new Map<string, { result: SearchSummary; timestamp: number }>();

  // Configuration
  private readonly MAX_FAILURES = 3;
  private readonly CIRCUIT_BREAKER_TIMEOUT = 180000; // 3 minutes (reduced from 5)
  private readonly CACHE_TTL = 300000; // 5 minutes
  private readonly HALF_OPEN_SUCCESS_THRESHOLD = 2;

  constructor() {
    this.initializeProviders();
  }

  /**
   * Initialize search providers in priority order
   */
  private initializeProviders(): void {
    // Primary provider - Serper (highest quality)
    this.providers.set('serper', {
      name: 'serper',
      priority: 1,
      enabled: !!process.env.SERPER_API_KEY,
      rateLimitDelay: 1000,
      maxResults: 20,
      timeout: 8000,
      search: this.searchWithSerper.bind(this),
    });

    // Secondary provider - Brave API (privacy-focused)
    this.providers.set('brave-api', {
      name: 'brave-api',
      priority: 2,
      enabled: !!process.env.BRAVE_API_KEY,
      rateLimitDelay: 2000,
      maxResults: 15,
      timeout: 10000,
      search: this.searchWithBraveAPI.bind(this),
    });

    // Tertiary provider - Brave MCP (fallback)
    this.providers.set('brave-mcp', {
      name: 'brave-mcp',
      priority: 3,
      enabled: !!process.env.BRAVE_API_KEY,
      rateLimitDelay: 3000,
      maxResults: 10,
      timeout: 12000,
      search: this.searchWithBraveMCP.bind(this),
    });

    // Backup provider - DuckDuckGo (no API key required)
    this.providers.set('duckduckgo', {
      name: 'duckduckgo',
      priority: 4,
      enabled: true,
      rateLimitDelay: 1500,
      maxResults: 8,
      timeout: 6000,
      search: this.searchWithDuckDuckGo.bind(this),
    });
  }

  /**
   * Perform comprehensive search with intelligent provider selection
   */
  async search(query: string, options: SearchOptions = {}): Promise<SearchSummary> {
    const startTime = Date.now();
    const {
      maxResults = 15,
      includeKnowledgeGraph = true,
      timeout = 20000,
      parallelExecution = true,
      maxProviders = 3,
      includeBackups = true,
    } = options;

    console.log(`🔍 Unified Search: "${query}" (parallel: ${parallelExecution})`);

    // Check cache first
    const cacheKey = `${query}-${maxResults}-${includeKnowledgeGraph}`;
    const cached = this.getCachedResult(cacheKey);
    if (cached) {
      console.log(`📦 Cache hit for query: "${query}"`);
      return cached;
    }

    const allResults: SearchResult[] = [];
    const sourcesUsed: string[] = [];
    const performance: SearchSummary['performance'] = { parallelExecution };
    let knowledgeGraph: SearchSummary['knowledgeGraph'];

    // Get available providers
    const availableProviders = this.getAvailableProviders(maxProviders);

    if (availableProviders.length === 0) {
      return this.createFallbackResult(query, startTime);
    }

    try {
      if (parallelExecution && availableProviders.length > 1) {
        // Parallel execution for better performance
        const results = await this.executeParallelSearch(
          query,
          availableProviders,
          maxResults,
          timeout
        );

        for (const result of results) {
          if (result.success && result.results.length > 0) {
            allResults.push(...result.results);
            sourcesUsed.push(result.provider);
            
            // Type-safe way to set the performance metric
            const metricKey = `${result.provider}Time` as keyof typeof performance;
            if (typeof result.duration === 'number') {
              (performance[metricKey] as number | undefined) = result.duration;
            }
          }
        }
      } else {
        // Sequential execution with intelligent fallback
        await this.executeSequentialSearch(
          query,
          availableProviders,
          maxResults,
          timeout,
          allResults,
          sourcesUsed,
          performance,
          includeBackups
        );
      }

      // Extract knowledge graph from Serper results
      if (includeKnowledgeGraph && sourcesUsed.includes('serper')) {
        knowledgeGraph = this.extractKnowledgeGraph(allResults);
      }

    } catch (error) {
      console.error('Search execution failed:', error);
      if (allResults.length === 0) {
        return this.createFallbackResult(query, startTime);
      }
    }

    // Consolidate and optimize results
    const summary = this.consolidateResults(
      query,
      allResults,
      sourcesUsed,
      startTime,
      performance,
      knowledgeGraph
    );

    // Cache the result
    this.cacheResult(cacheKey, summary);

    console.log(`✅ Unified search completed: ${summary.uniqueUrls} unique results from ${sourcesUsed.join(', ')}`);
    return summary;
  }

  /**
   * Get available providers sorted by priority
   */
  private getAvailableProviders(maxProviders: number): SearchProvider[] {
    return Array.from(this.providers.values())
      .filter(provider => provider.enabled && !this.isCircuitBreakerOpen(provider.name))
      .sort((a, b) => a.priority - b.priority)
      .slice(0, maxProviders);
  }

  /**
   * Execute parallel search across multiple providers
   */
  private async executeParallelSearch(
    query: string,
    providers: SearchProvider[],
    maxResults: number,
    timeout: number
  ): Promise<Array<{ provider: string; success: boolean; results: SearchResult[]; duration: number }>> {
    const searchPromises = providers.map(async (provider) => {
      const providerStartTime = Date.now();
      try {
        await this.respectRateLimit(provider.name, provider.rateLimitDelay);

        const results = await Promise.race([
          provider.search(query, Math.ceil(maxResults / providers.length)),
          new Promise<SearchResult[]>((_, reject) =>
            setTimeout(() => reject(new Error(`${provider.name} timeout`)), provider.timeout)
          )
        ]);

        this.recordSuccess(provider.name);
        return {
          provider: provider.name,
          success: true,
          results,
          duration: Date.now() - providerStartTime,
        };
      } catch (error) {
        this.recordFailure(provider.name, error);
        return {
          provider: provider.name,
          success: false,
          results: [],
          duration: Date.now() - providerStartTime,
        };
      }
    });

    // Wait for all searches to complete or timeout
    const timeoutPromise = new Promise<never>((_, reject) =>
      setTimeout(() => reject(new Error('Overall search timeout')), timeout)
    );

    try {
      return await Promise.race([
        Promise.allSettled(searchPromises).then(results =>
          results.map(result => result.status === 'fulfilled' ? result.value : {
            provider: 'unknown',
            success: false,
            results: [],
            duration: 0,
          })
        ),
        timeoutPromise,
      ]);
    } catch (error) {
      // Return partial results if available
      const settledResults = await Promise.allSettled(searchPromises);
      return settledResults.map(result => result.status === 'fulfilled' ? result.value : {
        provider: 'unknown',
        success: false,
        results: [],
        duration: 0,
      });
    }
  }

  /**
   * Execute sequential search with intelligent fallback
   */
  private async executeSequentialSearch(
    query: string,
    providers: SearchProvider[],
    maxResults: number,
    timeout: number,
    allResults: SearchResult[],
    sourcesUsed: string[],
    performance: SearchSummary['performance'],
    includeBackups: boolean
  ): Promise<void> {
    const timeoutPerProvider = Math.floor(timeout / providers.length);

    for (const provider of providers) {
      if (allResults.length >= maxResults) break;

      try {
        const providerStartTime = Date.now();
        await this.respectRateLimit(provider.name, provider.rateLimitDelay);

        const results = await Promise.race([
          provider.search(query, Math.ceil(maxResults * 0.6)),
          new Promise<SearchResult[]>((_, reject) =>
            setTimeout(() => reject(new Error(`${provider.name} timeout`)), timeoutPerProvider)
          )
        ]);

        if (results.length > 0) {
          allResults.push(...results);
          sourcesUsed.push(provider.name);
          performance[`${provider.name}Time` as keyof typeof performance] = Date.now() - providerStartTime;
          this.recordSuccess(provider.name);
        }

      } catch (error) {
        this.recordFailure(provider.name, error);
        console.warn(`${provider.name} search failed:`, error);

        // Try backup providers if primary fails
        if (includeBackups && provider.priority === 1 && allResults.length === 0) {
          await this.tryBackupProviders(query, allResults, sourcesUsed, performance);
        }
      }
    }
  }

  /**
   * Try backup providers when primary providers fail
   */
  private async tryBackupProviders(
    query: string,
    allResults: SearchResult[],
    sourcesUsed: string[],
    performance: SearchSummary['performance']
  ): Promise<void> {
    const backupProviders = Array.from(this.providers.values())
      .filter(p => p.enabled && p.priority > 3 && !sourcesUsed.includes(p.name))
      .slice(0, 2);

    for (const provider of backupProviders) {
      try {
        const providerStartTime = Date.now();
        await this.respectRateLimit(provider.name, provider.rateLimitDelay);

        const results = await provider.search(query, 5);
        if (results.length > 0) {
          allResults.push(...results);
          sourcesUsed.push(provider.name);
          performance[`${provider.name}Time` as keyof typeof performance] = Date.now() - providerStartTime;
          break; // Stop after first successful backup
        }
      } catch (error) {
        console.warn(`Backup provider ${provider.name} failed:`, error);
      }
    }
  }

  /**
   * Search using Serper API
   */
  private async searchWithSerper(query: string, maxResults: number = 10): Promise<SearchResult[]> {
    const apiKey = process.env.SERPER_API_KEY;
    if (!apiKey) {
      throw new Error('Serper API key not configured');
    }

    const response = await fetch('https://google.serper.dev/search', {
      method: 'POST',
      headers: {
        'X-API-KEY': apiKey,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        q: query,
        num: maxResults,
        gl: 'us',
        hl: 'en',
      }),
    });

    if (!response.ok) {
      throw new Error(`Serper API error: ${response.status}`);
    }

    const data = await response.json();
    const results: SearchResult[] = [];

    // Add knowledge graph first (highest relevance)
    if (data.knowledgeGraph) {
      results.push({
        title: `${data.knowledgeGraph.title} (Knowledge Graph)`,
        url: data.knowledgeGraph.website || '',
        snippet: data.knowledgeGraph.description || '',
        source: 'serper-kg',
        relevanceScore: 1.0,
        timestamp: Date.now(),
      });
    }

    // Add organic results
    if (data.organic) {
      results.push(...data.organic.map((result: any, index: number) => ({
        title: result.title || '',
        url: result.link || '',
        snippet: result.snippet || '',
        source: 'serper',
        relevanceScore: 0.95 - (index * 0.05),
        timestamp: Date.now(),
        metadata: {
          domain: this.extractDomain(result.link),
        },
      })));
    }

    return results;
  }

  /**
   * Search using Brave API
   */
  private async searchWithBraveAPI(query: string, maxResults: number = 10): Promise<SearchResult[]> {
    const apiKey = process.env.BRAVE_API_KEY;
    if (!apiKey) {
      throw new Error('Brave API key not configured');
    }

    const params = new URLSearchParams({
      q: query,
      count: maxResults.toString(),
      offset: '0',
      mkt: 'en-US',
      safesearch: 'moderate',
      text_decorations: 'true',
      spellcheck: 'true',
    });

    const response = await fetch(`https://api.search.brave.com/res/v1/web/search?${params}`, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'Accept-Encoding': 'gzip',
        'X-Subscription-Token': apiKey,
      },
    });

    if (!response.ok) {
      throw new Error(`Brave API error: ${response.status}`);
    }

    const data = await response.json();

    return (data.web?.results || []).map((result: any, index: number) => ({
      title: result.title || '',
      url: result.url || '',
      snippet: result.description || '',
      source: 'brave-api',
      relevanceScore: 0.9 - (index * 0.05),
      timestamp: Date.now(),
      metadata: {
        domain: this.extractDomain(result.url),
        age: result.age,
        language: result.language,
      },
    }));
  }

  /**
   * Search using Brave MCP
   */
  private async searchWithBraveMCP(query: string, maxResults: number = 10): Promise<SearchResult[]> {
    // Import MCP client dynamically to avoid circular dependencies
    const { performBraveWebSearch } = await import('./mcp-client');

    try {
      const response = await performBraveWebSearch(query, maxResults);

      return (response.results || []).map((result: any, index: number) => ({
        title: result.title || '',
        url: result.url || '',
        snippet: result.description || '',
        source: 'brave-mcp',
        relevanceScore: 0.85 - (index * 0.05),
        timestamp: Date.now(),
        metadata: {
          domain: this.extractDomain(result.url),
          language: result.language,
          age: result.age,
        },
      }));
    } catch (error) {
      console.warn('Brave MCP search failed:', error);
      throw error;
    }
  }

  /**
   * Search using DuckDuckGo (placeholder - would need actual implementation)
   */
  private async searchWithDuckDuckGo(query: string, maxResults: number = 8): Promise<SearchResult[]> {
    // This is a placeholder - in a real implementation, you would use a DuckDuckGo API or scraping
    // For now, return empty results to avoid errors
    console.warn('DuckDuckGo search not implemented - returning empty results');
    return [];
  }

  /**
   * Consolidate and optimize search results
   */
  private consolidateResults(
    query: string,
    allResults: SearchResult[],
    sourcesUsed: string[],
    startTime: number,
    performance: SearchSummary['performance'],
    knowledgeGraph?: SearchSummary['knowledgeGraph']
  ): SearchSummary {
    const totalSources = allResults.length;

    // Remove duplicates based on URL similarity and content
    const uniqueResults = this.removeDuplicates(allResults);

    // Sort by relevance and source priority
    const sortedResults = this.sortByRelevance(uniqueResults);

    return {
      query,
      results: sortedResults,
      totalSources,
      uniqueUrls: uniqueResults.length,
      searchTime: Date.now() - startTime,
      sourcesUsed,
      duplicatesRemoved: totalSources - uniqueResults.length,
      knowledgeGraph,
      performance,
    };
  }

  /**
   * Remove duplicate results based on URL similarity and content
   */
  private removeDuplicates(results: SearchResult[]): SearchResult[] {
    const seen = new Set<string>();
    const unique: SearchResult[] = [];

    for (const result of results) {
      // Create a key based on URL and title similarity
      const normalizedUrl = this.normalizeUrl(result.url);
      const normalizedTitle = result.title.toLowerCase().trim();
      const key = `${normalizedUrl}-${normalizedTitle.substring(0, 50)}`;

      if (!seen.has(key)) {
        seen.add(key);
        unique.push(result);
      }
    }

    return unique;
  }

  /**
   * Sort results by relevance score and source priority
   */
  private sortByRelevance(results: SearchResult[]): SearchResult[] {
    const sourcePriority = {
      'serper-kg': 1,
      'serper': 2,
      'brave-api': 3,
      'brave-mcp': 4,
      'duckduckgo': 5,
    };

    return results.sort((a, b) => {
      // First sort by relevance score
      if (a.relevanceScore !== b.relevanceScore) {
        return b.relevanceScore - a.relevanceScore;
      }

      // Then by source priority
      const aPriority = sourcePriority[a.source as keyof typeof sourcePriority] || 10;
      const bPriority = sourcePriority[b.source as keyof typeof sourcePriority] || 10;

      return aPriority - bPriority;
    });
  }

  /**
   * Extract knowledge graph from search results
   */
  private extractKnowledgeGraph(results: SearchResult[]): SearchSummary['knowledgeGraph'] | undefined {
    const kgResult = results.find(r => r.source === 'serper-kg');
    if (kgResult) {
      return {
        title: kgResult.title.replace(' (Knowledge Graph)', ''),
        description: kgResult.snippet,
        website: kgResult.url,
      };
    }
    return undefined;
  }

  /**
   * Create fallback result when all searches fail
   */
  private createFallbackResult(query: string, startTime: number): SearchSummary {
    return {
      query,
      results: [{
        title: 'Search Temporarily Unavailable',
        url: '',
        snippet: `Web search for "${query}" is temporarily unavailable. Please try again later or check your API configurations.`,
        source: 'fallback',
        relevanceScore: 0.1,
        timestamp: Date.now(),
      }],
      totalSources: 0,
      uniqueUrls: 0,
      searchTime: Date.now() - startTime,
      sourcesUsed: ['fallback'],
      duplicatesRemoved: 0,
      performance: { parallelExecution: false },
    };
  }

  /**
   * Normalize URL for duplicate detection
   */
  private normalizeUrl(url: string): string {
    try {
      const parsed = new URL(url);
      // Remove common tracking parameters and fragments
      parsed.search = '';
      parsed.hash = '';
      return parsed.toString().toLowerCase();
    } catch {
      return url.toLowerCase();
    }
  }

  /**
   * Extract domain from URL
   */
  private extractDomain(url: string): string {
    try {
      return new URL(url).hostname;
    } catch {
      return '';
    }
  }

  /**
   * Cache management
   */
  private getCachedResult(key: string): SearchSummary | null {
    const cached = this.resultCache.get(key);
    if (cached && Date.now() - cached.timestamp < this.CACHE_TTL) {
      return cached.result;
    }
    if (cached) {
      this.resultCache.delete(key);
    }
    return null;
  }

  private cacheResult(key: string, result: SearchSummary): void {
    // Limit cache size
    if (this.resultCache.size > 100) {
      const oldestKey = this.resultCache.keys().next().value;
      if (oldestKey) {
        this.resultCache.delete(oldestKey);
      }
    }

    this.resultCache.set(key, {
      result,
      timestamp: Date.now(),
    });
  }

  /**
   * Circuit breaker management
   */
  private isCircuitBreakerOpen(providerName: string): boolean {
    const breaker = this.circuitBreakers.get(providerName);
    if (!breaker) return false;

    // Check if enough time has passed to try again (half-open state)
    if (breaker.isOpen && Date.now() - breaker.lastFailure > this.CIRCUIT_BREAKER_TIMEOUT) {
      breaker.isOpen = false;
      breaker.failures = 0;
      console.log(`🔄 Circuit breaker for ${providerName} reset after timeout`);
    }

    return breaker.isOpen;
  }

  private recordFailure(providerName: string, error: any): void {
    const breaker = this.circuitBreakers.get(providerName) || {
      failures: 0,
      lastFailure: 0,
      isOpen: false,
      successCount: 0,
      lastSuccess: 0,
    };

    breaker.failures++;
    breaker.lastFailure = Date.now();
    breaker.successCount = 0; // Reset success count on failure

    if (breaker.failures >= this.MAX_FAILURES) {
      breaker.isOpen = true;
      console.warn(`🚨 Circuit breaker opened for ${providerName} after ${breaker.failures} failures. Will retry in ${this.CIRCUIT_BREAKER_TIMEOUT / 1000}s`);
    }

    this.circuitBreakers.set(providerName, breaker);
  }

  private recordSuccess(providerName: string): void {
    const breaker = this.circuitBreakers.get(providerName);
    if (breaker) {
      breaker.successCount++;
      breaker.lastSuccess = Date.now();

      // Reset circuit breaker after successful requests
      if (breaker.failures > 0 && breaker.successCount >= this.HALF_OPEN_SUCCESS_THRESHOLD) {
        breaker.failures = 0;
        breaker.isOpen = false;
        console.log(`✅ Circuit breaker reset for ${providerName} after ${breaker.successCount} successful requests`);
      }

      this.circuitBreakers.set(providerName, breaker);
    }
  }

  /**
   * Rate limiting
   */
  private async respectRateLimit(providerName: string, delayMs: number): Promise<void> {
    const lastCall = this.rateLimits.get(providerName) || 0;
    const timeSinceLastCall = Date.now() - lastCall;

    if (timeSinceLastCall < delayMs) {
      const waitTime = delayMs - timeSinceLastCall;
      await new Promise(resolve => setTimeout(resolve, waitTime));
    }

    this.rateLimits.set(providerName, Date.now());
  }

  /**
   * Get search engine health status
   */
  getHealthStatus(): Record<string, { status: string; failures: number; lastSuccess?: number }> {
    const status: Record<string, { status: string; failures: number; lastSuccess?: number }> = {};

    for (const [name, provider] of this.providers) {
      const breaker = this.circuitBreakers.get(name);
      const hasApiKey = name === 'duckduckgo' ||
                       (name.includes('serper') && !!process.env.SERPER_API_KEY) ||
                       (name.includes('brave') && !!process.env.BRAVE_API_KEY);

      status[name] = {
        status: !hasApiKey ? 'No API Key' :
                !provider.enabled ? 'Disabled' :
                breaker?.isOpen ? `Circuit Breaker Open (${breaker.failures} failures)` :
                'Available',
        failures: breaker?.failures || 0,
        lastSuccess: breaker?.lastSuccess,
      };
    }

    return status;
  }

  /**
   * Enable or disable a search provider
   */
  enableProvider(providerName: string, enabled: boolean): void {
    const provider = this.providers.get(providerName);
    if (provider) {
      provider.enabled = enabled;
      console.log(`${enabled ? 'Enabled' : 'Disabled'} search provider: ${providerName}`);
    }
  }

  /**
   * Clear cache
   */
  clearCache(): void {
    this.resultCache.clear();
    console.log('Search result cache cleared');
  }

  /**
   * Reset circuit breakers
   */
  resetCircuitBreakers(): void {
    this.circuitBreakers.clear();
    console.log('All circuit breakers reset');
  }
}

// Export singleton instance
export const unifiedSearchEngine = new UnifiedSearchEngine();
