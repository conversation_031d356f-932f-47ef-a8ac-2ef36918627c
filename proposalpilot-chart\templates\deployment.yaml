apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "proposalpilot.fullname" . }}
  labels:
    {{- include "proposalpilot.labels" . | nindent 4 }}
spec:
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
      {{- include "proposalpilot.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "proposalpilot.selectorLabels" . | nindent 8 }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "proposalpilot.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
        - name: {{ .Chart.Name }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          ports:
            - name: http
              containerPort: 3000
              protocol: TCP
          livenessProbe:
            httpGet:
              path: /
              port: http
          readinessProbe:
            httpGet:
              path: /
              port: http
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
          env:
            # API Keys
            - name: GEMINI_API_KEY
              valueFrom:
                secretKeyRef:
                  name: {{ include "proposalpilot.fullname" . }}-secrets
                  key: gemini-api-key
            - name: BRAVE_API_KEY
              valueFrom:
                secretKeyRef:
                  name: {{ include "proposalpilot.fullname" . }}-secrets
                  key: brave-api-key
            - name: SERPER_API_KEY
              valueFrom:
                secretKeyRef:
                  name: {{ include "proposalpilot.fullname" . }}-secrets
                  key: serper-api-key
            
            # Database Configuration
            - name: DATABASE_URL
              value: "postgresql://{{ .Values.postgresql.auth.username }}:$(POSTGRES_PASSWORD)@{{ include "proposalpilot.fullname" . }}-postgresql:5433/{{ .Values.postgresql.auth.database }}?schema=public"
            - name: POSTGRES_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ include "proposalpilot.fullname" . }}-secrets
                  key: postgres-password
            - name: POSTGRES_USER
              value: {{ .Values.postgresql.auth.username | quote }}
            - name: POSTGRES_DB
              value: {{ .Values.postgresql.auth.database | quote }}
            - name: POSTGRES_HOST
              value: {{ printf "%s-postgresql" (include "proposalpilot.fullname" .) | quote }}
            - name: POSTGRES_PORT
              value: "5433"
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
