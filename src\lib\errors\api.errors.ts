// src/lib/errors/api.errors.ts

export enum ErrorCode {
  // Validation Errors (400)
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  INVALID_REQUEST = 'INVALID_REQUEST',
  
  // Authentication/Authorization Errors (401/403)
  UNAUTHORIZED = 'UNAUTHORIZED',
  FORBIDDEN = 'FORBIDDEN',
  
  // Resource Errors (404)
  NOT_FOUND = 'NOT_FOUND',
  
  // Rate Limiting (429)
  RATE_LIMIT_EXCEEDED = 'RATE_LIMIT_EXCEEDED',
  
  // Proposal Generation Errors (5xx)
  PROPOSAL_GENERATION_FAILED = 'PROPOSAL_GENERATION_FAILED',
  CONTEXT_TOO_LARGE = 'CONTEXT_TOO_LARGE',
  INVALID_TEMPLATE = 'INVALID_TEMPLATE',
  GENERATION_TIMEOUT = 'GENERATION_TIMEOUT',
  
  // Server Errors (500)
  INTERNAL_SERVER_ERROR = 'INTERNAL_SERVER_ERROR',
  SERVICE_UNAVAILABLE = 'SERVICE_UNAVAILABLE',
}

export class ApiError extends Error {
  constructor(
    public statusCode: number,
    public code: ErrorCode,
    message: string,
    public details?: any
  ) {
    super(message);
    this.name = this.constructor.name;
    
    // Maintains proper stack trace for where our error was thrown (only available on V8)
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, this.constructor);
    }
  }

  toJSON() {
    return {
      code: this.code,
      message: this.message,
      ...(this.details && { details: this.details }),
      ...(process.env.NODE_ENV === 'development' && { stack: this.stack }),
    };
  }
}

// Specific error classes
export class ValidationError extends ApiError {
  constructor(message: string, details?: any) {
    super(400, ErrorCode.VALIDATION_ERROR, message, details);
  }
}

export class UnauthorizedError extends ApiError {
  constructor(message = 'Authentication required') {
    super(401, ErrorCode.UNAUTHORIZED, message);
  }
}

export class ForbiddenError extends ApiError {
  constructor(message = 'Insufficient permissions') {
    super(403, ErrorCode.FORBIDDEN, message);
  }
}

export class NotFoundError extends ApiError {
  constructor(resource: string) {
    super(404, ErrorCode.NOT_FOUND, `${resource} not found`);
  }
}

export class RateLimitError extends ApiError {
  constructor(retryAfter?: number) {
    super(
      429, 
      ErrorCode.RATE_LIMIT_EXCEEDED, 
      'Too many requests, please try again later',
      { retryAfter }
    );
  }
}

export class InternalServerError extends ApiError {
  constructor(message = 'An unexpected error occurred') {
    super(500, ErrorCode.INTERNAL_SERVER_ERROR, message);
  }
}

export class ServiceUnavailableError extends ApiError {
  constructor(service: string) {
    super(503, ErrorCode.SERVICE_UNAVAILABLE, `${service} is currently unavailable`);
  }
}

export class ProposalGenerationError extends ApiError {
  constructor(message: string, details?: any) {
    super(500, ErrorCode.PROPOSAL_GENERATION_FAILED, message, details);
  }
}

export class ContextTooLargeError extends ApiError {
  constructor(maxSize: number, actualSize: number) {
    super(413, ErrorCode.CONTEXT_TOO_LARGE, 'The request context is too large', {
      maxSize,
      actualSize,
      message: `Context size (${actualSize} tokens) exceeds maximum allowed (${maxSize} tokens)`
    });
  }
}

export class InvalidTemplateError extends ApiError {
  constructor(templateId: string, reason: string) {
    super(400, ErrorCode.INVALID_TEMPLATE, `Invalid template: ${templateId}`, { reason });
  }
}

export class GenerationTimeoutError extends ApiError {
  constructor(timeoutMs: number) {
    super(504, ErrorCode.GENERATION_TIMEOUT, 'Proposal generation timed out', {
      timeoutMs,
      message: `Generation exceeded timeout of ${timeoutMs}ms`
    });
  }
}

// Type guard for API errors
export function isApiError(error: unknown): error is ApiError {
  return error instanceof ApiError;
}
