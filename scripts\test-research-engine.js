/**
 * Test the Research Engine
 * Run with: node scripts/test-research-engine.js
 */

require('dotenv').config();

// Import the research engine (we'll simulate it since we can't import TS directly)
async function testResearchEngine() {
  console.log('🚀 Testing Research Engine');
  console.log('==========================\n');

  // Test query
  const query = 'artificial intelligence proposal writing tools';
  
  console.log(`🔍 Research Query: "${query}"`);
  console.log('⏳ Searching across multiple sources...\n');

  try {
    // Simulate the research engine functionality
    const results = await performResearch(query);
    
    console.log('📊 RESEARCH SUMMARY');
    console.log('==================');
    console.log(`✅ Total unique results: ${results.uniqueUrls}`);
    console.log(`🔗 Sources used: ${results.sourcesUsed.join(', ')}`);
    console.log(`⏱️  Search time: ${results.searchTime}ms`);
    
    if (results.knowledgeGraph) {
      console.log('\n📚 KNOWLEDGE GRAPH');
      console.log('==================');
      console.log(`📖 ${results.knowledgeGraph.title}`);
      console.log(`📄 ${results.knowledgeGraph.description.substring(0, 150)}...`);
      if (results.knowledgeGraph.website) {
        console.log(`🔗 ${results.knowledgeGraph.website}`);
      }
    }

    console.log('\n🔍 TOP SEARCH RESULTS');
    console.log('=====================');
    
    results.results.slice(0, 5).forEach((result, index) => {
      const sourceIcon = result.source === 'serper' ? '🔍' : 
                        result.source === 'brave' ? '🦁' : '📚';
      console.log(`\n${index + 1}. ${result.title} ${sourceIcon}`);
      console.log(`   🔗 ${result.url}`);
      console.log(`   📄 ${result.snippet.substring(0, 120)}...`);
      console.log(`   ⭐ Relevance: ${(result.relevanceScore * 100).toFixed(1)}%`);
    });

    console.log('\n💡 RESEARCH INSIGHTS');
    console.log('====================');
    
    const serperCount = results.results.filter(r => r.source === 'serper').length;
    const braveCount = results.results.filter(r => r.source === 'brave').length;
    const kgCount = results.results.filter(r => r.source === 'serper-kg').length;
    
    console.log(`📊 Source distribution:`);
    console.log(`   🔍 Serper: ${serperCount} results`);
    console.log(`   🦁 Brave: ${braveCount} results`);
    console.log(`   📚 Knowledge Graph: ${kgCount} results`);
    
    console.log(`\n🎯 Research quality:`);
    console.log(`   • Comprehensive coverage from ${results.sourcesUsed.length} APIs`);
    console.log(`   • ${results.uniqueUrls} unique sources (no duplicates)`);
    console.log(`   • Knowledge graph for authoritative information`);
    console.log(`   • Privacy-focused results from Brave`);
    console.log(`   • High-quality results from Serper`);

    console.log('\n🎉 Research Engine test completed successfully!');
    
  } catch (error) {
    console.error('❌ Research Engine test failed:', error.message);
  }
}

async function performResearch(query) {
  const startTime = Date.now();
  const allResults = [];
  const sourcesUsed = [];
  let knowledgeGraph = null;

  // Search with Serper
  try {
    const serperResults = await searchWithSerper(query);
    if (serperResults.length > 0) {
      allResults.push(...serperResults);
      sourcesUsed.push('serper');
      
      // Extract knowledge graph
      const kgResult = serperResults.find(r => r.source === 'serper-kg');
      if (kgResult) {
        knowledgeGraph = {
          title: kgResult.title.replace(' (Knowledge Graph)', ''),
          description: kgResult.snippet,
          website: kgResult.url,
        };
      }
    }
  } catch (error) {
    console.warn('⚠️  Serper search failed:', error.message);
  }

  // Search with Brave
  try {
    const braveResults = await searchWithBrave(query);
    if (braveResults.length > 0) {
      allResults.push(...braveResults);
      sourcesUsed.push('brave');
    }
  } catch (error) {
    console.warn('⚠️  Brave search failed:', error.message);
  }

  // Remove duplicates and sort
  const uniqueResults = removeDuplicates(allResults);
  const sortedResults = sortByRelevance(uniqueResults);

  return {
    query,
    results: sortedResults,
    totalSources: allResults.length,
    uniqueUrls: uniqueResults.length,
    searchTime: Date.now() - startTime,
    sourcesUsed,
    knowledgeGraph,
  };
}

async function searchWithSerper(query) {
  const apiKey = process.env.SERPER_API_KEY;
  if (!apiKey) throw new Error('Serper API key not configured');

  const response = await fetch('https://google.serper.dev/search', {
    method: 'POST',
    headers: {
      'X-API-KEY': apiKey,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      q: query,
      num: 8,
      gl: 'us',
      hl: 'en',
    }),
  });

  if (!response.ok) throw new Error(`Serper API error: ${response.status}`);

  const data = await response.json();
  const results = [];

  if (data.knowledgeGraph) {
    results.push({
      title: `${data.knowledgeGraph.title} (Knowledge Graph)`,
      url: data.knowledgeGraph.website || '',
      snippet: data.knowledgeGraph.description || '',
      source: 'serper-kg',
      relevanceScore: 1.0,
      timestamp: Date.now(),
    });
  }

  if (data.organic) {
    results.push(...data.organic.map((result, index) => ({
      title: result.title || '',
      url: result.link || '',
      snippet: result.snippet || '',
      source: 'serper',
      relevanceScore: 0.95 - (index * 0.05),
      timestamp: Date.now(),
    })));
  }

  return results;
}

async function searchWithBrave(query) {
  const apiKey = process.env.BRAVE_API_KEY;
  if (!apiKey) throw new Error('Brave API key not configured');

  const params = new URLSearchParams({
    q: query,
    count: '6',
    offset: '0',
    mkt: 'en-US',
    safesearch: 'moderate',
    text_decorations: 'true',
    spellcheck: 'true',
  });

  const response = await fetch(`https://api.search.brave.com/res/v1/web/search?${params}`, {
    method: 'GET',
    headers: {
      'Accept': 'application/json',
      'Accept-Encoding': 'gzip',
      'X-Subscription-Token': apiKey,
    },
  });

  if (!response.ok) throw new Error(`Brave API error: ${response.status}`);

  const data = await response.json();

  return (data.web?.results || []).map((result, index) => ({
    title: result.title || '',
    url: result.url || '',
    snippet: result.description || '',
    source: 'brave',
    relevanceScore: 0.9 - (index * 0.05),
    timestamp: Date.now(),
  }));
}

function removeDuplicates(results) {
  const seen = new Set();
  return results.filter(result => {
    const normalizedUrl = normalizeUrl(result.url);
    if (seen.has(normalizedUrl)) return false;
    seen.add(normalizedUrl);
    return true;
  });
}

function normalizeUrl(url) {
  try {
    const parsed = new URL(url);
    ['utm_source', 'utm_medium', 'utm_campaign', 'fbclid', 'gclid'].forEach(param => {
      parsed.searchParams.delete(param);
    });
    return parsed.toString().toLowerCase();
  } catch {
    return url.toLowerCase();
  }
}

function sortByRelevance(results) {
  return results.sort((a, b) => {
    if (a.source === 'serper-kg') return -1;
    if (b.source === 'serper-kg') return 1;
    return b.relevanceScore - a.relevanceScore;
  });
}

// Run the test
testResearchEngine();
