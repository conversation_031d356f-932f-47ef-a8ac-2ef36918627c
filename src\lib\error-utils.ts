// src/lib/error-utils.ts

export interface ErrorWithMessage {
  message: string;
  digest?: string;
  statusCode?: number;
  stack?: string;
}

// Type guard to check if an object has a digest property
function hasDigest(error: unknown): error is { digest: string } {
  return typeof error === 'object' && error !== null && 'digest' in error;
}

export function logServerError(error: unknown, context: string = 'Server Error') {
  const errorObj: ErrorWithMessage = error instanceof Error 
    ? { 
        message: error.message, 
        stack: error.stack,
        digest: hasDigest(error) ? error.digest : undefined,
      } 
    : { message: String(error) };

  console.error(`[${new Date().toISOString()}] ${context}:`, {
    message: errorObj.message,
    digest: errorObj.digest,
    stack: process.env.NODE_ENV === 'development' ? errorObj.stack : undefined,
  });

  return errorObj;
}

export function createSafeError(message: string, error?: unknown): ErrorWithMessage {
  if (error instanceof Error) {
    return {
      message: `${message}: ${error.message}`,
      stack: error.stack,
      digest: hasDigest(error) ? error.digest : undefined,
    };
  }
  return { message };
}

export function isExpectedError(error: unknown): boolean {
  if (!(error instanceof Error)) return false;
  
  // Add any error types that you consider expected/not critical
  const expectedErrors = [
    'ValidationError',
    'NotFoundError',
    'UnauthorizedError',
  ];
  
  return expectedErrors.some(type => error.name === type || error.constructor.name === type);
}
