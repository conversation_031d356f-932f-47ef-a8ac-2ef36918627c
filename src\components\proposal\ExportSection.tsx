"use client";

import React from 'react';
import { Button } from '@/components/ui/button';
import { SectionCard } from './SectionCard';
import { Download, FileText } from 'lucide-react';

interface ExportSectionProps {
  understandingReqs: string;
  solutionOverview: string;
  oemSolutions: string[];
  technicalImplementation: string;
  comprehensiveProposal: string;
  appliedSuggestions: Array<{ text: string }>;
  mermaidSvg: string;
  onExportMarkdown: () => void;
}

export const ExportSection: React.FC<ExportSectionProps> = ({
  understandingReqs,
  solutionOverview,
  oemSolutions,
  technicalImplementation,
  comprehensiveProposal,
  appliedSuggestions,
  mermaidSvg,
  onExportMarkdown,
}) => {
  const hasContent = understandingReqs || solutionOverview || comprehensiveProposal;

  // Generate preview of what will be exported
  const generatePreviewContent = () => {
    if (comprehensiveProposal) {
      return comprehensiveProposal.substring(0, 200) + '...';
    }

    let preview = '';
    if (understandingReqs) {
      preview += `## Understanding the Requirements\n${understandingReqs.substring(0, 100)}...\n\n`;
    }
    if (solutionOverview) {
      preview += `## Solution Overview\n${solutionOverview.substring(0, 100)}...\n\n`;
    }
    if (oemSolutions.length > 0) {
      preview += `### Suggested OEM Solutions\n${oemSolutions.slice(0, 2).map(s => `- ${s}`).join('\n')}\n\n`;
    }
    if (technicalImplementation) {
      preview += `## Technical Implementation\n${technicalImplementation.substring(0, 100)}...\n\n`;
    }
    if (appliedSuggestions.length > 0) {
      preview += `## Applied Suggestions\n${appliedSuggestions.slice(0, 2).map((s, i) => `${i + 1}. ${s.text}`).join('\n\n')}\n\n`;
    }

    return preview || 'No content to preview yet.';
  };

  return (
    <SectionCard
      title="7. Export Proposal"
      description="Download your generated proposal as a Markdown file."
      icon={Download}
      actionButton={
        <Button
          onClick={onExportMarkdown}
          disabled={!hasContent}
          className="w-full"
        >
          <Download className="mr-2 h-4 w-4" />
          Export as Markdown
        </Button>
      }
    >
      <div className="space-y-4">
        <div className="p-4 bg-muted/50 rounded-md">
          <div className="flex items-center space-x-2 mb-2">
            <FileText className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm font-medium">Export Preview</span>
          </div>
          <pre className="text-xs text-muted-foreground whitespace-pre-wrap max-h-40 overflow-y-auto">
            {generatePreviewContent()}
          </pre>
        </div>

        <div className="text-sm text-muted-foreground space-y-1">
          <p><strong>What will be included:</strong></p>
          <ul className="list-disc list-inside space-y-1 ml-2">
            {comprehensiveProposal && (
              <li>Complete comprehensive proposal (AI-generated)</li>
            )}
            {!comprehensiveProposal && (
              <>
                {understandingReqs && <li>Understanding the Requirements section</li>}
                {solutionOverview && <li>Solution Overview section</li>}
                {oemSolutions.length > 0 && <li>Suggested OEM Solutions ({oemSolutions.length} items)</li>}
                {technicalImplementation && <li>Technical Implementation section</li>}
                {mermaidSvg && <li>Architecture diagram (as SVG)</li>}
                {appliedSuggestions.length > 0 && <li>Applied Suggestions ({appliedSuggestions.length} items)</li>}
              </>
            )}
          </ul>
        </div>

        {!hasContent && (
          <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-md dark:bg-yellow-950/20 dark:border-yellow-800">
            <p className="text-sm text-yellow-800 dark:text-yellow-200">
              <strong>Note:</strong> Generate some proposal content first before exporting.
            </p>
          </div>
        )}
      </div>
    </SectionCard>
  );
};
